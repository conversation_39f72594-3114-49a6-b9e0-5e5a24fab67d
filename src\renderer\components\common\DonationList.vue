<template>
  <div class="donation-container">
    <div class="qrcode-container">
      <div class="description">
        <p>{{ t('donation.description') }}</p>
        <p>{{ t('donation.message') }}</p>
          <n-button type="primary" @click="toDonateList">
            <template #icon>
              <i class="ri-wechat-line"></i>
            </template>
            {{ t('donation.toDonateList') }}
          </n-button> 
      </div>
      <div class="qrcode-grid">
        <div class="qrcode-item">
          <n-image
            :src="wechatPublic"
            :alt="t('common.wechatPublic')"
            class="qrcode-image"
            preview-disabled
          />
          <span class="qrcode-label" :key="'wechat-' + currentLocale">{{ t('common.wechatPublic') }}</span>
        </div>
        
        <div class="qrcode-item">
          <n-image
            :src="lingsiApp"
            :alt="t('common.lingsiApp')"
            class="qrcode-image"
            preview-disabled
          />
          <span class="qrcode-label" :key="'lingsi-' + currentLocale">{{ t('common.lingsiApp') }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';

import wechatPublic from '@/assets/gzh.png';
import lingsiApp from '@/assets/xcx.png';

const { t, locale } = useI18n();
const currentLocale = computed(() => locale.value);

const toDonateList = () => {
  // 修改为空函数，不再跳转到外部链接
};
</script>

<style lang="scss" scoped>
.donation-container {
  @apply w-full overflow-hidden flex flex-col gap-4;
}

.qrcode-container {
  @apply p-5 rounded-lg shadow-sm bg-light-100 dark:bg-gray-800/5 backdrop-blur-sm border border-gray-200 dark:border-gray-700/10;
  
  .description {
    @apply text-center text-sm text-gray-600 dark:text-gray-300 mb-4;
    
    p {
      @apply mb-2;
    }
  }
  
  .qrcode-grid {
    @apply flex justify-between items-center gap-4 flex-wrap;
    
    .qrcode-item {
      @apply flex flex-col items-center gap-2;
      
      .qrcode-image {
        @apply w-36 h-36 rounded-lg border border-gray-200 dark:border-gray-700/10 shadow-sm transition-transform duration-200 hover:scale-105;
      }
      
      .qrcode-label {
        @apply text-sm text-gray-600 dark:text-gray-300;
      }
    }
  }
}
</style>
