import axios from 'axios';

// 直接设置GD音乐台API地址
export const GD_MUSIC_API = 'https://music-api.gdstudio.xyz/api.php';

// 从环境变量获取API地址，如果没有则使用默认值
const baseURL = `${import.meta.env.VITE_API_MUSIC || 'https://music-api.gdstudio.xyz/api.php'}`;
const request = axios.create({
  baseURL,
  timeout: 15000, // 增加超时时间
  headers: {
    'User-Agent': 'AlgerMusicPlayer/4.8.1',
    'Referer': 'https://music.gdstudio.xyz/',
    'Origin': 'https://music.gdstudio.xyz'
  },
  // 允许跨域请求携带凭证
  withCredentials: true
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    console.log(`[Music API] 请求: ${config.url}`);
    return config;
  },
  (error) => {
    // 当请求异常时做一些处理
    console.error('[Music API] 请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    console.log(`[Music API] 响应成功: ${response.config.url}`);
    return response;
  },
  (error) => {
    console.error('[Music API] 响应错误:', error);
    if (error.response) {
      // 服务器返回了错误状态码
      console.error(`[Music API] 状态码: ${error.response.status}`);
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      console.error('[Music API] 无响应');
    } else {
      // 请求配置出错
      console.error('[Music API] 请求配置错误:', error.message);
    }
    return Promise.reject(error);
  }
);

export default request;
