import { BrowserWindow, screen } from 'electron';
import Store from 'electron-store';

// 默认窗口尺寸常量
export const DEFAULT_MAIN_WIDTH = 1000;
export const DEFAULT_MAIN_HEIGHT = 700;
export const DEFAULT_MINI_WIDTH = 300;
export const DEFAULT_MINI_HEIGHT = 80;

export interface WindowState {
  width: number;
  height: number;
  x?: number;
  y?: number;
  isMaximized?: boolean;
}

const store = new Store<WindowState>({
  name: 'window-state',
  defaults: {
    width: DEFAULT_MAIN_WIDTH,
    height: DEFAULT_MAIN_HEIGHT
  }
});

/**
 * 窗口大小管理模块
 * 保存和恢复窗口大小和位置
 */
class WindowSize {
  /**
   * 获取保存的窗口状态
   * @returns 窗口状态
   */
  getSavedState(): WindowState {
    return store.store;
  }

  /**
   * 保存窗口状态
   * @param window BrowserWindow实例
   */
  saveState(window: BrowserWindow): void {
    if (!window.isMinimized() && !window.isDestroyed()) {
      const isMaximized = window.isMaximized();
      const bounds = window.getBounds();

      store.set({
        width: bounds.width,
        height: bounds.height,
        x: isMaximized ? undefined : bounds.x,
        y: isMaximized ? undefined : bounds.y,
        isMaximized
      });
    }
  }

  /**
   * 应用保存的窗口状态
   * @param window BrowserWindow实例
   */
  applyState(window: BrowserWindow): void {
    const state = this.getSavedState();
    const { width, height, x, y, isMaximized } = state;

    // 检查窗口位置是否在可见屏幕上
    let isVisibleOnAnyScreen = false;
    if (x !== undefined && y !== undefined) {
      const displays = screen.getAllDisplays();
      isVisibleOnAnyScreen = displays.some(display => {
        const bounds = display.bounds;
        return (
          x >= bounds.x &&
          y >= bounds.y &&
          x + width <= bounds.x + bounds.width &&
          y + height <= bounds.y + bounds.height
        );
      });
    }

    // 设置窗口大小和位置
    if (isVisibleOnAnyScreen && x !== undefined && y !== undefined) {
      window.setBounds({
        width,
        height,
        x,
        y
      });
    } else {
      window.setSize(width, height);
      window.center();
    }

    // 如果之前是最大化状态，则恢复最大化
    if (isMaximized) {
      window.maximize();
    }
  }

  /**
   * 设置窗口大小监听器
   * @param window BrowserWindow实例
   */
  setupWindowSizeListener(window: BrowserWindow): void {
    // 监听窗口大小和位置变化
    const saveState = () => this.saveState(window);
    window.on('resize', saveState);
    window.on('move', saveState);
    window.on('close', saveState);
  }

  /**
   * 初始化窗口大小管理器
   */
  initialize(): void {
    console.log('窗口大小管理器初始化完成');
  }
}

const windowSize = new WindowSize();

/**
 * 初始化窗口大小管理器
 */
export function initWindowSizeManager(): void {
  windowSize.initialize();
}

/**
 * 获取窗口状态
 * @returns 窗口状态
 */
export function getWindowState(): WindowState {
  return windowSize.getSavedState();
}

/**
 * 保存窗口状态
 * @param window BrowserWindow实例
 * @returns 保存的窗口状态
 */
export function saveWindowState(window: BrowserWindow): WindowState {
  windowSize.saveState(window);
  return windowSize.getSavedState();
}

/**
 * 应用初始窗口状态
 * @param window BrowserWindow实例
 */
export function applyInitialState(window: BrowserWindow): void {
  windowSize.applyState(window);
}

/**
 * 初始化窗口大小处理器
 * @param window BrowserWindow实例
 */
export function initWindowSizeHandlers(window: BrowserWindow): void {
  windowSize.setupWindowSizeListener(window);
}

/**
 * 应用内容缩放
 * @param window BrowserWindow实例
 * @param zoomFactor 缩放因子
 */
export function applyContentZoom(window: BrowserWindow, zoomFactor: number): void {
  window.webContents.setZoomFactor(zoomFactor);
}

/**
 * 获取窗口选项
 * @returns 窗口选项对象
 */
export function getWindowOptions(): Electron.BrowserWindowConstructorOptions {
  const state = getWindowState();
  return {
    width: state.width || DEFAULT_MAIN_WIDTH,
    height: state.height || DEFAULT_MAIN_HEIGHT,
    minWidth: Math.max(DEFAULT_MAIN_WIDTH * 0.5, 600),
    minHeight: Math.max(DEFAULT_MAIN_HEIGHT * 0.5, 400),
    x: state.x,
    y: state.y,
    show: false,
    autoHideMenuBar: true,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    }
  };
}

export default windowSize;