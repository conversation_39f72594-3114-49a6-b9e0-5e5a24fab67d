// 缓存名称，更改版本号可以强制更新缓存
const CACHE_NAME = 'suixin-music-cache-v2';
const STATIC_CACHE = 'suixin-static-v2';
const DYNAMIC_CACHE = 'suixin-dynamic-v2';

// 需要缓存的静态资源列表
const urlsToCache = [
  '/',
  '/index.html',
  '/favicon.ico',
  '/manifest.json',
  '/apple-touch-icon.png',
  '/icons/icon-72x72.png',
  '/icons/icon-96x96.png',
  '/icons/icon-128x128.png',
  '/icons/icon-144x144.png',
  '/icons/icon-152x152.png',
  '/icons/icon-192x192.png',
  '/icons/icon-384x384.png',
  '/icons/icon-512x512.png',
  '/offline.html'
];

// 需要网络优先的资源
const networkFirstUrls = [
  '/api/',
  '/music/',
  '/search'
];

// 需要缓存优先的资源
const cacheFirstUrls = [
  '/assets/',
  '/icons/',
  '.css',
  '.js',
  '.png',
  '.jpg',
  '.jpeg',
  '.svg',
  '.woff',
  '.woff2'
];

// 安装Service Worker
self.addEventListener('install', (event) => {
  console.log('Service Worker 安装中...');
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('正在缓存静态资源...');
        return cache.addAll(urlsToCache);
      })
      .then(() => {
        console.log('静态资源缓存完成');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('缓存静态资源失败:', error);
      })
  );
});

// 激活Service Worker
self.addEventListener('activate', (event) => {
  console.log('Service Worker 激活中...');
  event.waitUntil(
    Promise.all([
      // 清理旧缓存
      caches.keys().then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('清除旧缓存:', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      }),
      // 立即控制所有客户端
      self.clients.claim()
    ]).then(() => {
      console.log('Service Worker 激活完成');
    })
  );
});

// 判断请求类型的辅助函数
function isStaticAsset(url) {
  return cacheFirstUrls.some(pattern => url.includes(pattern));
}

function isApiRequest(url) {
  return networkFirstUrls.some(pattern => url.includes(pattern));
}

// 缓存优先策略
function cacheFirst(request) {
  return caches.match(request).then((response) => {
    if (response) {
      return response;
    }
    return fetch(request).then((response) => {
      if (response.status === 200) {
        const responseClone = response.clone();
        caches.open(STATIC_CACHE).then((cache) => {
          cache.put(request, responseClone);
        });
      }
      return response;
    });
  });
}

// 网络优先策略
function networkFirst(request) {
  return fetch(request).then((response) => {
    if (response.status === 200) {
      const responseClone = response.clone();
      caches.open(DYNAMIC_CACHE).then((cache) => {
        cache.put(request, responseClone);
      });
    }
    return response;
  }).catch(() => {
    return caches.match(request);
  });
}

// 处理fetch请求
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = request.url;

  // 跳过非GET请求
  if (request.method !== 'GET') {
    return;
  }

  // 跳过chrome-extension和其他协议
  if (!url.startsWith('http')) {
    return;
  }

  event.respondWith(
    (async () => {
      try {
        // 静态资源使用缓存优先策略
        if (isStaticAsset(url)) {
          return await cacheFirst(request);
        }

        // API请求使用网络优先策略
        if (isApiRequest(url)) {
          return await networkFirst(request);
        }

        // 页面导航请求
        if (request.mode === 'navigate') {
          try {
            const response = await fetch(request);
            return response;
          } catch (error) {
            // 网络失败时返回缓存的首页或离线页面
            const cachedResponse = await caches.match('/') || await caches.match('/offline.html');
            return cachedResponse || new Response('离线模式，请检查网络连接', {
              status: 503,
              statusText: 'Service Unavailable'
            });
          }
        }

        // 默认策略：先尝试网络，失败则使用缓存
        try {
          const response = await fetch(request);
          if (response.status === 200) {
            const responseClone = response.clone();
            const cache = await caches.open(DYNAMIC_CACHE);
            cache.put(request, responseClone);
          }
          return response;
        } catch (error) {
          const cachedResponse = await caches.match(request);
          return cachedResponse || new Response('资源不可用', {
            status: 404,
            statusText: 'Not Found'
          });
        }
      } catch (error) {
        console.error('Service Worker fetch error:', error);
        return new Response('服务异常', {
          status: 500,
          statusText: 'Internal Server Error'
        });
      }
    })()
  );
});

// 后台同步
self.addEventListener('sync', (event) => {
  if (event.tag === 'background-sync') {
    console.log('执行后台同步');
    // 这里可以添加后台同步逻辑
  }
});

// 推送通知
self.addEventListener('push', (event) => {
  if (event.data) {
    const data = event.data.json();
    const options = {
      body: data.body,
      icon: '/icons/icon-192x192.png',
      badge: '/icons/icon-72x72.png',
      vibrate: [100, 50, 100],
      data: data.data || {}
    };

    event.waitUntil(
      self.registration.showNotification(data.title, options)
    );
  }
});

// 通知点击处理
self.addEventListener('notificationclick', (event) => {
  event.notification.close();

  event.waitUntil(
    clients.openWindow(event.notification.data.url || '/')
  );
});