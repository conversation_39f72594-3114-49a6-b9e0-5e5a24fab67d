declare module 'howler' {
  export class Howl {
    constructor(options: any);
    play(id?: number): number;
    pause(id?: number): this;
    stop(id?: number): this;
    unload(): this;
    on(event: string, listener: Function): this;
    off(event: string, listener?: Function): this;
    once(event: string, listener: Function): this;
    volume(vol?: number, id?: number): number | this;
    fade(from: number, to: number, duration: number, id?: number): this;
    seek(seek?: number, id?: number): number | this;
    loop(loop?: boolean, id?: number): boolean | this;
    state(): string;
    playing(id?: number): boolean;
    duration(id?: number): number;
    load(): this;
    mute(muted?: boolean, id?: number): boolean | this;
    rate(rate?: number, id?: number): number | this;
    _sounds: Array<any>;
    _state: string;
    stereo(pan?: number, id?: number): this | number;
    pos(x?: number, y?: number, z?: number, id?: number): this | any[];
    orientation(x?: number, y?: number, z?: number, id?: number): this | any[];
    pannerAttr(o?: any, id?: number): this | any;
  }

  export class Howler {
    static ctx: AudioContext;
    static masterGain: GainNode;
    static volume(vol?: number): number | this;
    static mute(muted?: boolean): boolean | this;
    static stop(): this;
    static usingWebAudio: boolean;
  }
} 