<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <title>离线模式 - 随心听</title>
    <link rel="icon" href="/favicon.ico">
    <link rel="apple-touch-icon" href="/icons/icon-192x192.png">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 50%, #388e3c 100%);
            color: white;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: env(safe-area-inset-top) env(safe-area-inset-right) env(safe-area-inset-bottom) env(safe-area-inset-left);
            overflow: hidden;
        }

        .container {
            max-width: 400px;
            padding: 40px 20px;
            text-align: center;
            animation: fadeInUp 0.6s ease-out;
        }

        .logo {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 40px;
            animation: pulse 2s infinite;
        }

        h1 {
            font-size: 28px;
            font-weight: 300;
            margin-bottom: 16px;
            letter-spacing: -0.5px;
        }

        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.5;
        }

        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
        }

        .status-icon {
            font-size: 24px;
            margin-bottom: 10px;
        }

        .status-text {
            font-size: 14px;
            opacity: 0.8;
        }

        .actions {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 14px 28px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn:hover, .btn:active {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: transparent;
            border-color: rgba(255, 255, 255, 0.2);
        }

        .network-status {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.3);
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            backdrop-filter: blur(10px);
        }

        .online {
            color: #4CAF50;
        }

        .offline {
            color: #f44336;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
                opacity: 1;
            }
            50% {
                transform: scale(1.05);
                opacity: 0.8;
            }
        }

        @media (max-width: 480px) {
            .container {
                padding: 20px 16px;
            }

            h1 {
                font-size: 24px;
            }

            .logo {
                width: 60px;
                height: 60px;
                font-size: 30px;
            }
        }

        /* PWA 模式适配 */
        @media (display-mode: standalone) {
            body {
                padding-top: max(20px, env(safe-area-inset-top));
            }
        }
    </style>
</head>
<body>
    <div class="network-status" id="networkStatus">
        <span id="statusText">检查网络状态...</span>
    </div>

    <div class="container">
        <div class="logo">🎵</div>
        <h1>随心听</h1>
        <p class="subtitle">您当前处于离线状态</p>

        <div class="status">
            <div class="status-icon">📡</div>
            <div class="status-text" id="connectionStatus">正在尝试重新连接...</div>
        </div>

        <div class="actions">
            <button class="btn" onclick="retryConnection()">重新连接</button>
            <a href="/" class="btn btn-secondary">返回首页</a>
        </div>
    </div>

    <script>
        // 网络状态检测
        function updateNetworkStatus() {
            const statusElement = document.getElementById('statusText');
            const networkStatus = document.getElementById('networkStatus');

            if (navigator.onLine) {
                statusElement.textContent = '已连接';
                networkStatus.className = 'network-status online';
                // 如果网络恢复，自动跳转
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            } else {
                statusElement.textContent = '离线';
                networkStatus.className = 'network-status offline';
            }
        }

        // 重试连接
        function retryConnection() {
            const statusText = document.getElementById('connectionStatus');

            statusText.textContent = '正在重新连接...';

            // 尝试加载一个小资源来测试网络
            fetch('/favicon.ico?' + Date.now(), {
                method: 'HEAD',
                cache: 'no-cache'
            })
            .then(() => {
                statusText.textContent = '连接成功！正在跳转...';
                setTimeout(() => {
                    window.location.href = '/';
                }, 500);
            })
            .catch(() => {
                statusText.textContent = '连接失败，请检查网络设置';
                setTimeout(() => {
                    statusText.textContent = '正在尝试重新连接...';
                }, 2000);
            });
        }

        // 监听网络状态变化
        window.addEventListener('online', updateNetworkStatus);
        window.addEventListener('offline', updateNetworkStatus);

        // 初始化
        updateNetworkStatus();

        // 定期检查网络状态
        setInterval(updateNetworkStatus, 5000);

        // 自动重试
        setInterval(() => {
            if (!navigator.onLine) {
                retryConnection();
            }
        }, 10000);
    </script>
</body>
</html>