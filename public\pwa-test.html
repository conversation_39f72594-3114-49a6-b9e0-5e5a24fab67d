<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <title>PWA 测试 - 随心听</title>
    
    <!-- PWA 相关 -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#4CAF50">
    
    <!-- iOS Safari 专用配置 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="随心听">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: white;
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 40px;
            color: #4CAF50;
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #f44336;
            color: white;
        }
        
        .status.warning {
            background: #ff9800;
            color: white;
        }
        
        .button {
            background: white;
            color: #4CAF50;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #fff;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        /* PWA 模式样式 */
        @media all and (display-mode: standalone) {
            body {
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
            }
            
            .pwa-indicator {
                display: block !important;
            }
        }
        
        .pwa-indicator {
            display: none;
            background: #4CAF50;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="pwa-indicator">
        🎉 PWA 模式已激活！地址栏已隐藏
    </div>
    
    <div class="container">
        <div class="header">
            <div class="logo">🎵</div>
            <h1>随心听 PWA 测试</h1>
            <p>Safari 地址栏隐藏测试页面</p>
        </div>
        
        <div class="test-section">
            <h3>📱 设备检测</h3>
            <div class="test-item">
                <span>设备类型</span>
                <span class="status" id="deviceType">检测中...</span>
            </div>
            <div class="test-item">
                <span>浏览器</span>
                <span class="status" id="browser">检测中...</span>
            </div>
            <div class="test-item">
                <span>PWA 模式</span>
                <span class="status" id="pwaMode">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 PWA 功能检测</h3>
            <div class="test-item">
                <span>Service Worker</span>
                <span class="status" id="serviceWorker">检测中...</span>
            </div>
            <div class="test-item">
                <span>Manifest</span>
                <span class="status" id="manifest">检测中...</span>
            </div>
            <div class="test-item">
                <span>安装提示</span>
                <span class="status" id="installPrompt">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📏 视口信息</h3>
            <div class="test-item">
                <span>屏幕尺寸</span>
                <span id="screenSize">检测中...</span>
            </div>
            <div class="test-item">
                <span>视口尺寸</span>
                <span id="viewportSize">检测中...</span>
            </div>
            <div class="test-item">
                <span>安全区域</span>
                <span id="safeArea">检测中...</span>
            </div>
        </div>
        
        <button class="button" onclick="testAddressBarHiding()">
            测试地址栏隐藏
        </button>
        
        <button class="button" onclick="window.location.href='/'">
            返回主应用
        </button>
        
        <div class="instructions">
            <h3>📱 Safari 添加到主屏幕步骤：</h3>
            <ol>
                <li>在 Safari 中打开此页面</li>
                <li>点击底部的分享按钮 📤</li>
                <li>向下滚动找到"添加到主屏幕"</li>
                <li>点击"添加"确认</li>
                <li>从主屏幕打开应用，检查地址栏是否隐藏</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            let deviceType = 'Unknown';
            
            if (/iPad/.test(userAgent)) deviceType = 'iPad';
            else if (/iPhone/.test(userAgent)) deviceType = 'iPhone';
            else if (/iPod/.test(userAgent)) deviceType = 'iPod';
            else if (/Android/.test(userAgent)) deviceType = 'Android';
            else if (/Windows/.test(userAgent)) deviceType = 'Windows';
            else if (/Mac/.test(userAgent)) deviceType = 'Mac';
            
            document.getElementById('deviceType').textContent = deviceType;
            document.getElementById('deviceType').className = 'status success';
        }
        
        // 浏览器检测
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browser = 'Unknown';
            
            if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
                browser = 'Safari';
            } else if (/Chrome/.test(userAgent)) {
                browser = 'Chrome';
            } else if (/Firefox/.test(userAgent)) {
                browser = 'Firefox';
            } else if (/Edge/.test(userAgent)) {
                browser = 'Edge';
            }
            
            document.getElementById('browser').textContent = browser;
            document.getElementById('browser').className = 'status success';
        }
        
        // PWA 模式检测
        function detectPWAMode() {
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
            const isIOSStandalone = window.navigator.standalone === true;
            const isPWA = isStandalone || isIOSStandalone;
            
            const element = document.getElementById('pwaMode');
            if (isPWA) {
                element.textContent = '✅ PWA 模式';
                element.className = 'status success';
                document.querySelector('.pwa-indicator').style.display = 'block';
            } else {
                element.textContent = '❌ 浏览器模式';
                element.className = 'status error';
            }
        }
        
        // Service Worker 检测
        function detectServiceWorker() {
            const element = document.getElementById('serviceWorker');
            if ('serviceWorker' in navigator) {
                element.textContent = '✅ 支持';
                element.className = 'status success';
            } else {
                element.textContent = '❌ 不支持';
                element.className = 'status error';
            }
        }
        
        // Manifest 检测
        async function detectManifest() {
            const element = document.getElementById('manifest');
            try {
                const response = await fetch('/manifest.json');
                if (response.ok) {
                    element.textContent = '✅ 已加载';
                    element.className = 'status success';
                } else {
                    element.textContent = '❌ 加载失败';
                    element.className = 'status error';
                }
            } catch (error) {
                element.textContent = '❌ 不可用';
                element.className = 'status error';
            }
        }
        
        // 安装提示检测
        function detectInstallPrompt() {
            const element = document.getElementById('installPrompt');
            if ('beforeinstallprompt' in window) {
                element.textContent = '✅ 支持';
                element.className = 'status success';
            } else {
                element.textContent = '⚠️ Safari 不支持';
                element.className = 'status warning';
            }
        }
        
        // 视口信息
        function updateViewportInfo() {
            document.getElementById('screenSize').textContent = 
                `${screen.width} × ${screen.height}`;
            document.getElementById('viewportSize').textContent = 
                `${window.innerWidth} × ${window.innerHeight}`;
            
            // 安全区域检测
            const computedStyle = getComputedStyle(document.documentElement);
            const safeAreaTop = computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0px';
            const safeAreaBottom = computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0px';
            document.getElementById('safeArea').textContent = 
                `顶部: ${safeAreaTop}, 底部: ${safeAreaBottom}`;
        }
        
        // 测试地址栏隐藏
        function testAddressBarHiding() {
            if (window.pageYOffset === 0) {
                window.scrollTo(0, 1);
            }
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
            }, 100);
            
            alert('已尝试隐藏地址栏。如果在PWA模式下，地址栏应该已经隐藏。');
        }
        
        // 初始化检测
        function initDetection() {
            detectDevice();
            detectBrowser();
            detectPWAMode();
            detectServiceWorker();
            detectManifest();
            detectInstallPrompt();
            updateViewportInfo();
        }
        
        // 页面加载完成后执行检测
        document.addEventListener('DOMContentLoaded', initDetection);
        window.addEventListener('resize', updateViewportInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateViewportInfo, 500);
        });
    </script>
</body>
</html>
