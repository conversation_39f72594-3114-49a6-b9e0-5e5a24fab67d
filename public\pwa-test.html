<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <title>PWA 测试 - 随心听</title>
    
    <!-- PWA 相关 -->
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#4CAF50">
    
    <!-- iOS Safari 专用配置 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="随心听">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #4CAF50, #2E7D32);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            width: 100px;
            height: 100px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(255, 255, 255, 0.2);
        }
        
        .test-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            backdrop-filter: blur(10px);
        }
        
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .test-item:last-child {
            border-bottom: none;
        }
        
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.success {
            background: #4CAF50;
            color: white;
        }
        
        .status.error {
            background: #f44336;
            color: white;
        }
        
        .status.warning {
            background: #ff9800;
            color: white;
        }
        
        .button {
            background: white;
            color: #4CAF50;
            border: none;
            padding: 15px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            width: 100%;
            margin: 10px 0;
            transition: all 0.3s ease;
        }
        
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .instructions h3 {
            margin-bottom: 15px;
            color: #fff;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin-bottom: 10px;
            line-height: 1.5;
        }
        
        /* PWA 模式样式 */
        @media all and (display-mode: standalone) {
            body {
                padding-top: env(safe-area-inset-top);
                padding-bottom: env(safe-area-inset-bottom);
            }
            
            .pwa-indicator {
                display: block !important;
            }
        }
        
        .pwa-indicator {
            display: none;
            background: #4CAF50;
            color: white;
            padding: 10px;
            text-align: center;
            font-weight: bold;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="pwa-indicator">
        🎉 PWA 模式已激活！地址栏已隐藏
    </div>
    
    <div class="container">
        <div class="header">
            <div class="logo">
                <svg viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg" style="width: 80px; height: 80px;">
                    <!-- 音符背景 -->
                    <defs>
                        <linearGradient id="noteGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="noteGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
                            <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#673AB7;stop-opacity:1" />
                        </linearGradient>
                        <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                            <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
                            <stop offset="50%" style="stop-color:#00BCD4;stop-opacity:1" />
                            <stop offset="100%" style="stop-color:#9C27B0;stop-opacity:1" />
                        </linearGradient>
                    </defs>

                    <!-- 五线谱线条 -->
                    <g stroke="url(#lineGradient)" stroke-width="3" opacity="0.8">
                        <line x1="20" y1="60" x2="180" y2="50" />
                        <line x1="20" y1="80" x2="180" y2="70" />
                        <line x1="20" y1="100" x2="180" y2="90" />
                        <line x1="20" y1="120" x2="180" y2="110" />
                        <line x1="20" y1="140" x2="180" y2="130" />
                    </g>

                    <!-- 音符1 -->
                    <g fill="url(#noteGradient1)">
                        <circle cx="50" cy="130" r="12" />
                        <rect x="62" y="90" width="3" height="40" />
                    </g>

                    <!-- 音符2 -->
                    <g fill="url(#noteGradient2)">
                        <circle cx="150" cy="80" r="15" />
                        <rect x="165" y="40" width="4" height="40" />
                    </g>

                    <!-- 装饰性小音符 -->
                    <g fill="#E0E0E0" opacity="0.6">
                        <circle cx="90" cy="110" r="8" />
                        <rect x="98" y="85" width="2" height="25" />
                    </g>

                    <!-- 文字 -->
                    <text x="100" y="175" text-anchor="middle" fill="white" font-family="Arial, sans-serif" font-size="16" font-weight="bold">随心听</text>
                </svg>
            </div>
            <h1>随心听 PWA 测试</h1>
            <p>Safari 地址栏隐藏测试页面</p>
        </div>
        
        <div class="test-section">
            <h3>📱 设备检测</h3>
            <div class="test-item">
                <span>设备类型</span>
                <span class="status" id="deviceType">检测中...</span>
            </div>
            <div class="test-item">
                <span>浏览器</span>
                <span class="status" id="browser">检测中...</span>
            </div>
            <div class="test-item">
                <span>PWA 模式</span>
                <span class="status" id="pwaMode">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🔧 PWA 功能检测</h3>
            <div class="test-item">
                <span>Service Worker</span>
                <span class="status" id="serviceWorker">检测中...</span>
            </div>
            <div class="test-item">
                <span>Manifest</span>
                <span class="status" id="manifest">检测中...</span>
            </div>
            <div class="test-item">
                <span>安装提示</span>
                <span class="status" id="installPrompt">检测中...</span>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📏 视口信息</h3>
            <div class="test-item">
                <span>屏幕尺寸</span>
                <span id="screenSize">检测中...</span>
            </div>
            <div class="test-item">
                <span>视口尺寸</span>
                <span id="viewportSize">检测中...</span>
            </div>
            <div class="test-item">
                <span>安全区域</span>
                <span id="safeArea">检测中...</span>
            </div>
        </div>
        
        <button class="button" onclick="testAddressBarHiding()">
            🔄 测试地址栏隐藏
        </button>

        <button class="button" onclick="enableFullscreenMode()">
            📱 启用全屏模式
        </button>

        <button class="button" onclick="testScrollHiding()">
            📜 测试滚动隐藏
        </button>

        <button class="button" onclick="window.location.href='/'">
            🏠 返回主应用
        </button>
        
        <div class="instructions">
            <h3>📱 Safari 添加到主屏幕步骤：</h3>
            <ol>
                <li>在 Safari 中打开此页面</li>
                <li>点击底部的分享按钮 📤</li>
                <li>向下滚动找到"添加到主屏幕"</li>
                <li>点击"添加"确认</li>
                <li>从主屏幕打开应用，检查地址栏是否隐藏</li>
            </ol>
        </div>
    </div>
    
    <script>
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            let deviceType = 'Unknown';
            
            if (/iPad/.test(userAgent)) deviceType = 'iPad';
            else if (/iPhone/.test(userAgent)) deviceType = 'iPhone';
            else if (/iPod/.test(userAgent)) deviceType = 'iPod';
            else if (/Android/.test(userAgent)) deviceType = 'Android';
            else if (/Windows/.test(userAgent)) deviceType = 'Windows';
            else if (/Mac/.test(userAgent)) deviceType = 'Mac';
            
            document.getElementById('deviceType').textContent = deviceType;
            document.getElementById('deviceType').className = 'status success';
        }
        
        // 浏览器检测
        function detectBrowser() {
            const userAgent = navigator.userAgent;
            let browser = 'Unknown';
            
            if (/Safari/.test(userAgent) && !/Chrome/.test(userAgent)) {
                browser = 'Safari';
            } else if (/Chrome/.test(userAgent)) {
                browser = 'Chrome';
            } else if (/Firefox/.test(userAgent)) {
                browser = 'Firefox';
            } else if (/Edge/.test(userAgent)) {
                browser = 'Edge';
            }
            
            document.getElementById('browser').textContent = browser;
            document.getElementById('browser').className = 'status success';
        }
        
        // PWA 模式检测
        function detectPWAMode() {
            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;
            const isIOSStandalone = window.navigator.standalone === true;
            const isPWA = isStandalone || isIOSStandalone;
            
            const element = document.getElementById('pwaMode');
            if (isPWA) {
                element.textContent = '✅ PWA 模式';
                element.className = 'status success';
                document.querySelector('.pwa-indicator').style.display = 'block';
            } else {
                element.textContent = '❌ 浏览器模式';
                element.className = 'status error';
            }
        }
        
        // Service Worker 检测
        function detectServiceWorker() {
            const element = document.getElementById('serviceWorker');
            if ('serviceWorker' in navigator) {
                element.textContent = '✅ 支持';
                element.className = 'status success';
            } else {
                element.textContent = '❌ 不支持';
                element.className = 'status error';
            }
        }
        
        // Manifest 检测
        async function detectManifest() {
            const element = document.getElementById('manifest');
            try {
                const response = await fetch('/manifest.json');
                if (response.ok) {
                    element.textContent = '✅ 已加载';
                    element.className = 'status success';
                } else {
                    element.textContent = '❌ 加载失败';
                    element.className = 'status error';
                }
            } catch (error) {
                element.textContent = '❌ 不可用';
                element.className = 'status error';
            }
        }
        
        // 安装提示检测
        function detectInstallPrompt() {
            const element = document.getElementById('installPrompt');
            if ('beforeinstallprompt' in window) {
                element.textContent = '✅ 支持';
                element.className = 'status success';
            } else {
                element.textContent = '⚠️ Safari 不支持';
                element.className = 'status warning';
            }
        }
        
        // 视口信息
        function updateViewportInfo() {
            document.getElementById('screenSize').textContent = 
                `${screen.width} × ${screen.height}`;
            document.getElementById('viewportSize').textContent = 
                `${window.innerWidth} × ${window.innerHeight}`;
            
            // 安全区域检测
            const computedStyle = getComputedStyle(document.documentElement);
            const safeAreaTop = computedStyle.getPropertyValue('env(safe-area-inset-top)') || '0px';
            const safeAreaBottom = computedStyle.getPropertyValue('env(safe-area-inset-bottom)') || '0px';
            document.getElementById('safeArea').textContent = 
                `顶部: ${safeAreaTop}, 底部: ${safeAreaBottom}`;
        }
        
        // 测试地址栏隐藏
        function testAddressBarHiding() {
            console.log('🧪 开始测试地址栏隐藏...');

            // 方法1: 传统scrollTo
            if (window.pageYOffset === 0) {
                window.scrollTo(0, 1);
            }

            // 方法2: requestAnimationFrame
            requestAnimationFrame(() => {
                window.scrollTo(0, 1);
                setTimeout(() => {
                    window.scrollTo(0, 0);
                }, 50);
            });

            // 方法3: 强制触发事件
            setTimeout(() => {
                window.dispatchEvent(new Event('resize'));
                window.dispatchEvent(new Event('scroll'));
            }, 100);

            // 方法4: iOS 15+ 视口处理
            if (window.visualViewport) {
                document.documentElement.style.height = window.visualViewport.height + 'px';
            }

            alert('✅ 已执行地址栏隐藏测试！请检查地址栏是否隐藏。');
        }

        // 启用全屏模式
        function enableFullscreenMode() {
            console.log('🚀 启用全屏模式...');

            // 应用全屏样式
            const styles = `
                html, body {
                    height: 100vh !important;
                    height: 100dvh !important;
                    margin: 0 !important;
                    padding: 0 !important;
                    overflow: hidden !important;
                    position: fixed !important;
                    width: 100% !important;
                    top: 0 !important;
                    left: 0 !important;
                }
            `;

            const styleSheet = document.createElement('style');
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);

            document.documentElement.classList.add('fullscreen-active');
            document.body.classList.add('fullscreen-active');

            alert('✅ 全屏模式已启用！');
        }

        // 测试滚动隐藏
        function testScrollHiding() {
            console.log('📜 测试滚动隐藏...');

            // 创建临时内容用于滚动
            const tempDiv = document.createElement('div');
            tempDiv.style.height = '200vh';
            tempDiv.style.background = 'linear-gradient(to bottom, transparent, rgba(255,255,255,0.1))';
            tempDiv.innerHTML = '<p style="padding: 50px; text-align: center; color: white;">滚动测试内容</p>';
            document.body.appendChild(tempDiv);

            // 滚动一点
            window.scrollTo(0, 100);

            setTimeout(() => {
                // 滚动回顶部并尝试隐藏地址栏
                window.scrollTo(0, 1);

                setTimeout(() => {
                    window.scrollTo(0, 0);
                    document.body.removeChild(tempDiv);
                    alert('✅ 滚动隐藏测试完成！');
                }, 500);
            }, 1000);
        }
        
        // 初始化检测
        function initDetection() {
            detectDevice();
            detectBrowser();
            detectPWAMode();
            detectServiceWorker();
            detectManifest();
            detectInstallPrompt();
            updateViewportInfo();
        }
        
        // 页面加载完成后执行检测
        document.addEventListener('DOMContentLoaded', initDetection);
        window.addEventListener('resize', updateViewportInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(updateViewportInfo, 500);
        });
    </script>
</body>
</html>
