<template>
  <div class="relative inline-block">
    <n-popover trigger="hover" placement="top" :show-arrow="true" :raw="true" :delay="100">
      <template #trigger>
        <slot>
          <n-button
            quaternary
            class="inline-flex items-center gap-2 px-4 py-2 transition-all duration-300 hover:-translate-y-0.5"
          >
            {{ t('comp.coffee.title') }}
          </n-button>
        </slot>
      </template>

      <div class="p-6 rounded-lg shadow-lg bg-light dark:bg-gray-800">
        <div class="flex gap-10">
          <div class="flex flex-col items-center gap-2">
            <n-image
              :src="gzhQR"
              alt="微信公众号"
              class="w-32 h-32 rounded-lg cursor-none"
              preview-disabled
            />
            <span class="text-sm text-gray-700 dark:text-gray-200">微信公众号</span>
          </div>
          <div class="flex flex-col items-center gap-2">
            <n-image
              :src="xcxQR"
              alt="灵思测评小程序"
              class="w-32 h-32 rounded-lg cursor-none"
              preview-disabled
            />
            <span class="text-sm text-gray-700 dark:text-gray-200">灵思测评小程序</span>
          </div>
        </div>
      </div>
    </n-popover>
  </div>
</template>

<script setup>
import { NButton, NImage, NPopover } from 'naive-ui';
import { useI18n } from 'vue-i18n';

import gzh from '@/assets/gzh.png';
import xcx from '@/assets/xcx.png';

const { t } = useI18n();

defineProps({
  gzhQR: {
    type: String,
    default: gzh
  },
  xcxQR: {
    type: String,
    default: xcx
  }
});
</script>
