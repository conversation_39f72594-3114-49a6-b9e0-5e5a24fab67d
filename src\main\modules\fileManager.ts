import { app } from 'electron';
import fs from 'fs';
import path from 'path';

/**
 * 文件管理器模块
 * 处理应用程序的文件读写操作
 */
class FileManager {
	/**
	 * 获取应用程序的数据目录
	 * @returns 数据目录路径
	 */
	getAppDataPath() {
		return app.getPath('userData');
	}

	/**
	 * 确保目录存在，如果不存在则创建
	 * @param dirPath 目录路径
	 */
	ensureDirectoryExists(dirPath: string) {
		if (!fs.existsSync(dirPath)) {
			fs.mkdirSync(dirPath, { recursive: true });
		}
	}

	/**
	 * 读取JSON文件
	 * @param filePath 文件路径
	 * @param defaultValue 默认值，如果文件不存在则返回此值
	 * @returns 解析后的JSON对象
	 */
	readJsonFile<T>(filePath: string, defaultValue: T): T {
		try {
			if (fs.existsSync(filePath)) {
				const data = fs.readFileSync(filePath, 'utf8');
				return JSON.parse(data) as T;
			}
		} catch (error) {
			console.error(`读取JSON文件失败: ${filePath}`, error);
		}
		return defaultValue;
	}

	/**
	 * 写入JSON文件
	 * @param filePath 文件路径
	 * @param data 要写入的数据
	 * @returns 是否写入成功
	 */
	writeJsonFile(filePath: string, data: any): boolean {
		try {
			const dirPath = path.dirname(filePath);
			this.ensureDirectoryExists(dirPath);
			fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
			return true;
		} catch (error) {
			console.error(`写入JSON文件失败: ${filePath}`, error);
			return false;
		}
	}

	/**
	 * 删除文件
	 * @param filePath 文件路径
	 * @returns 是否删除成功
	 */
	deleteFile(filePath: string): boolean {
		try {
			if (fs.existsSync(filePath)) {
				fs.unlinkSync(filePath);
				return true;
			}
			return false;
		} catch (error) {
			console.error(`删除文件失败: ${filePath}`, error);
			return false;
		}
	}

	/**
	 * 读取文件
	 * @param filePath 文件路径
	 * @returns 文件内容或null（如果读取失败）
	 */
	readFile(filePath: string): Buffer | null {
		try {
			if (fs.existsSync(filePath)) {
				return fs.readFileSync(filePath);
			}
		} catch (error) {
			console.error(`读取文件失败: ${filePath}`, error);
		}
		return null;
	}

	/**
	 * 写入文件
	 * @param filePath 文件路径
	 * @param data 要写入的数据
	 * @returns 是否写入成功
	 */
	writeFile(filePath: string, data: Buffer | string): boolean {
		try {
			const dirPath = path.dirname(filePath);
			this.ensureDirectoryExists(dirPath);
			fs.writeFileSync(filePath, data);
			return true;
		} catch (error) {
			console.error(`写入文件失败: ${filePath}`, error);
			return false;
		}
	}

	/**
	 * 初始化应用程序所需的文件夹
	 */
	initialize() {
		// 确保应用数据目录存在
		const appDataPath = this.getAppDataPath();
		this.ensureDirectoryExists(appDataPath);

		// 创建下载目录
		const downloadPath = path.join(appDataPath, 'downloads');
		this.ensureDirectoryExists(downloadPath);

		// 创建缓存目录
		const cachePath = path.join(appDataPath, 'cache');
		this.ensureDirectoryExists(cachePath);

		// 创建日志目录
		const logsPath = path.join(appDataPath, 'logs');
		this.ensureDirectoryExists(logsPath);

		console.log('文件管理器初始化完成');
	}
}

const fileManager = new FileManager();

/**
 * 初始化文件管理器
 * 创建应用程序所需的文件夹结构
 */
export function initializeFileManager(): void {
	fileManager.initialize();
}

export default fileManager;