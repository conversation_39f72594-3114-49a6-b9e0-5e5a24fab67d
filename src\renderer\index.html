<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no"
    />

    <!-- 防止Safari自动缩放 -->
    <meta name="mobile-web-app-capable" content="yes" />
    <meta name="mobile-web-app-status-bar-style" content="black-translucent" />

    <!-- 强制全屏显示 -->
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent" />
    <meta name="apple-mobile-web-app-title" content="随心听" />
    <meta name="apple-touch-fullscreen" content="yes" />

    <!-- 禁用Safari的智能应用横幅 -->
    <meta name="apple-itunes-app" content="app-id=, app-argument=" />

    <!-- 防止电话号码自动识别 -->
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no" />

    <!-- 确保PWA全屏模式 -->
    <meta name="msapplication-tap-highlight" content="no" />
    <meta name="msapplication-TileColor" content="#4CAF50" />
    <meta name="application-name" content="随心听" />

    <!-- SEO 元数据 -->
    <title>随心听</title>
    <meta
      name="description"
      content="随心听-随时随地，好音乐不等待 - 一款免费的在线音乐播放器，支持在线播放、歌词显示、音乐下载等功能。提供海量音乐资源，让您随时随地享受音乐。"
    />
    <meta
      name="keywords"
      content="随心听, 音乐播放器, 在线音乐, 免费音乐, 歌词显示, 音乐下载, 网易云音乐"
    />

    <!-- 作者信息 -->
    <meta name="author" content="随心听" />
    <meta name="author-url" content="https://github.com/algerkong" />

    <!-- PWA 相关 -->
    <link rel="manifest" href="/manifest.json" />
    <meta name="theme-color" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: light)" content="#4CAF50" />
    <meta name="theme-color" media="(prefers-color-scheme: dark)" content="#2E7D32" />

    <!-- iOS设备相关（已在上方配置，此处删除重复） -->

    <!-- Apple Touch Icons -->
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
    <link rel="apple-touch-icon" sizes="57x57" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="60x60" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="72x72" href="/icons/icon-72x72.png" />
    <link rel="apple-touch-icon" sizes="76x76" href="/icons/icon-96x96.png" />
    <link rel="apple-touch-icon" sizes="114x114" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="120x120" href="/icons/icon-128x128.png" />
    <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png" />
    <link rel="apple-touch-icon" sizes="152x152" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="167x167" href="/icons/icon-152x152.png" />
    <link rel="apple-touch-icon" sizes="180x180" href="/icons/icon-192x192.png" />

    <!-- iOS启动屏幕 -->
    <!-- iPhone X, XS, 11 Pro -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 375px) and (device-height: 812px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone XR, 11 -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 2)" />
    <!-- iPhone XS Max, 11 Pro Max -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 414px) and (device-height: 896px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone 12 mini -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 360px) and (device-height: 780px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone 12, 12 Pro -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 390px) and (device-height: 844px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPhone 12 Pro Max -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 428px) and (device-height: 926px) and (-webkit-device-pixel-ratio: 3)" />
    <!-- iPad -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 768px) and (device-height: 1024px) and (-webkit-device-pixel-ratio: 2)" />
    <!-- iPad Pro -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png"
          media="(device-width: 834px) and (device-height: 1112px) and (-webkit-device-pixel-ratio: 2)" />
    <!-- 通用启动屏幕 -->
    <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png" />

    <!-- 资源预加载 -->
    <link rel="preload" href="./assets/icon/iconfont.css" as="style" />
    <link rel="preload" href="./assets/css/base.css" as="style" />

    <!-- 样式表 -->
    <link rel="stylesheet" href="./assets/icon/iconfont.css" />
    <link rel="stylesheet" href="./assets/css/base.css" />
    <link rel="stylesheet" href="./assets/css/pwa.css" />

    <!-- 动画配置 -->
    <style>
      :root {
        --animate-delay: 0.5s;
      }
    </style>
  </head>

  <body>
    <div id="app"></div>
    <script type="module" src="./main.ts"></script>
    <!-- PWA 和 Service Worker 相关脚本 -->
    <script>
      // 注册Service Worker
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', () => {
          navigator.serviceWorker.register('/sw.js')
            .then(registration => {
              console.log('Service Worker 注册成功:', registration.scope);

              // 检查更新
              registration.addEventListener('updatefound', () => {
                const newWorker = registration.installing;
                newWorker.addEventListener('statechange', () => {
                  if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                    // 有新版本可用
                    console.log('新版本可用，请刷新页面');
                  }
                });
              });
            })
            .catch(error => {
              console.log('Service Worker 注册失败:', error);
            });
        });
      }

      // iOS Safari 特定优化
      if (navigator.userAgent.match(/iPhone|iPad|iPod/i)) {
        // 防止双击缩放
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
          const now = (new Date()).getTime();
          if (now - lastTouchEnd <= 300) {
            event.preventDefault();
          }
          lastTouchEnd = now;
        }, false);

        // 防止橡皮筋效果
        document.addEventListener('touchmove', function (event) {
          if (event.scale !== 1) {
            event.preventDefault();
          }
        }, { passive: false });

        // 通用地址栏隐藏 - 支持所有浏览器和设备
        function initUniversalFullscreen() {
          // 设备检测
          const userAgent = navigator.userAgent;
          const isIOS = /iPad|iPhone|iPod/.test(userAgent);
          const isAndroid = /Android/.test(userAgent);
          const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
          const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
          const isPWA = window.matchMedia('(display-mode: standalone)').matches ||
                        window.navigator.standalone === true;

          console.log('🔍 设备检测:', { isIOS, isAndroid, isSafari, isMobile, isPWA });

          // 设置全屏样式
          function applyFullscreenStyles() {
            // 强制全屏样式
            const styles = `
              html, body {
                height: 100vh !important;
                height: 100dvh !important;
                margin: 0 !important;
                padding: 0 !important;
                overflow: hidden !important;
                position: fixed !important;
                width: 100% !important;
                top: 0 !important;
                left: 0 !important;
              }
              #app {
                height: 100vh !important;
                height: 100dvh !important;
                overflow: hidden !important;
                position: relative !important;
                width: 100% !important;
              }
            `;

            const styleSheet = document.createElement('style');
            styleSheet.textContent = styles;
            document.head.appendChild(styleSheet);

            // 添加类名
            document.documentElement.classList.add('fullscreen-active');
            document.body.classList.add('fullscreen-active');
          }

          // 地址栏隐藏核心逻辑
          function hideAddressBar() {
            // 如果是PWA模式，只应用样式
            if (isPWA) {
              console.log('✅ PWA模式，应用全屏样式');
              applyFullscreenStyles();
              return;
            }

            console.log('🚀 开始隐藏地址栏...');

            // 应用全屏样式
            applyFullscreenStyles();

            // 滚动隐藏地址栏
            const scrollHide = () => {
              if (window.pageYOffset === 0) {
                window.scrollTo(0, 1);
              }

              // 使用requestAnimationFrame确保执行
              requestAnimationFrame(() => {
                window.scrollTo(0, 1);

                // 再次尝试
                setTimeout(() => {
                  window.scrollTo(0, 0);
                  window.dispatchEvent(new Event('resize'));
                }, 50);
              });

              // iOS 15+ 视口处理
              if (window.visualViewport) {
                document.documentElement.style.height = window.visualViewport.height + 'px';
              }
            };

            // 立即执行
            scrollHide();

            // 多次尝试
            setTimeout(scrollHide, 100);
            setTimeout(scrollHide, 300);
            setTimeout(scrollHide, 500);
            setTimeout(scrollHide, 1000);
          }

          return hideAddressBar;
        }

        // 初始化全屏功能
        const hideAddressBar = initUniversalFullscreen();

        // 立即执行
        console.log('🚀 立即执行地址栏隐藏');
        hideAddressBar();

        // 页面加载完成后执行
        window.addEventListener('load', function() {
          console.log('📄 页面加载完成，执行地址栏隐藏');
          setTimeout(hideAddressBar, 100);
        });

        // DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
          console.log('🏗️ DOM加载完成，执行地址栏隐藏');
          hideAddressBar();
        });

        // 用户交互后执行
        let interactionCount = 0;
        function handleUserInteraction() {
          interactionCount++;
          console.log(`👆 用户交互 #${interactionCount}，执行地址栏隐藏`);
          hideAddressBar();

          // 前几次交互多次尝试
          if (interactionCount <= 3) {
            setTimeout(hideAddressBar, 200);
            setTimeout(hideAddressBar, 500);
          }
        }

        // 监听多种交互事件
        document.addEventListener('touchstart', handleUserInteraction, { passive: true });
        document.addEventListener('touchend', handleUserInteraction, { passive: true });
        document.addEventListener('click', handleUserInteraction);
        document.addEventListener('scroll', handleUserInteraction, { passive: true });

        // 方向变化时执行
        window.addEventListener('orientationchange', function() {
          console.log('🔄 屏幕方向变化，执行地址栏隐藏');
          setTimeout(hideAddressBar, 100);
          setTimeout(hideAddressBar, 500);
          setTimeout(hideAddressBar, 1000);
        });

        // 窗口大小变化时执行
        window.addEventListener('resize', function() {
          console.log('📏 窗口大小变化，执行地址栏隐藏');
          setTimeout(hideAddressBar, 100);
        });

        // 页面可见性变化时执行
        document.addEventListener('visibilitychange', function() {
          if (!document.hidden) {
            console.log('👁️ 页面变为可见，执行地址栏隐藏');
            setTimeout(hideAddressBar, 300);
          }
        });

        // 窗口获得焦点时执行
        window.addEventListener('focus', function() {
          console.log('🎯 窗口获得焦点，执行地址栏隐藏');
          setTimeout(hideAddressBar, 200);
        });

        // 定期检查和执行（前30秒内）
        let checkCount = 0;
        const intervalCheck = setInterval(function() {
          checkCount++;
          console.log(`⏰ 定期检查 #${checkCount}，执行地址栏隐藏`);
          hideAddressBar();

          if (checkCount >= 10) { // 10次后停止定期检查
            clearInterval(intervalCheck);
            console.log('⏹️ 停止定期检查');
          }
        }, 3000); // 每3秒检查一次
      }

      // PWA 安装提示
      let deferredPrompt;
      window.addEventListener('beforeinstallprompt', (e) => {
        e.preventDefault();
        deferredPrompt = e;
        console.log('PWA 安装提示已准备');
      });

      // 检测是否在PWA模式下运行
      function isPWA() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone === true;
      }

      // PWA模式检测和初始化
      function initPWAMode() {
        const isPWAMode = isPWA();

        if (isPWAMode) {
          document.documentElement.classList.add('pwa-mode');
          document.body.classList.add('pwa-mode');
          console.log('运行在PWA模式下');

          // PWA模式下的特殊处理
          document.documentElement.style.height = '100vh';
          document.documentElement.style.overflow = 'hidden';
          document.body.style.height = '100vh';
          document.body.style.overflow = 'hidden';
          document.body.style.position = 'fixed';
          document.body.style.width = '100%';
          document.body.style.top = '0';
          document.body.style.left = '0';
        } else {
          console.log('运行在浏览器模式下');
        }

        // 添加设备类型类
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
          document.documentElement.classList.add('ios-device');
        }

        if (/^((?!chrome|android).)*safari/i.test(navigator.userAgent)) {
          document.documentElement.classList.add('safari-browser');
        }
      }

      // 立即执行PWA初始化
      initPWAMode();

      // DOM加载完成后再次检查
      document.addEventListener('DOMContentLoaded', initPWAMode);
    </script>
  </body>
</html>
