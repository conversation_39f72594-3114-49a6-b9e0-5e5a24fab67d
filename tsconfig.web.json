{"extends": "@electron-toolkit/tsconfig/tsconfig.web.json", "include": ["src/preload/*.d.ts", "src/renderer/**/*", "src/renderer/**/*.vue", "src/i18n/**/*", "src/main/modules/config.ts", "src/main/modules/shortcuts.ts"], "compilerOptions": {"composite": true, "target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "skipLibCheck": true, "resolveJsonModule": true, "esModuleInterop": true, "baseUrl": ".", "types": ["naive-ui/volar", "./src/renderer/auto-imports.d.ts", "./src/renderer/components.d.ts", "./src/renderer/types/shims-howler.d.ts", "./src/renderer/types/shims-vueuse.d.ts"], "paths": {"@/*": ["src/renderer/*"], "@renderer/*": ["src/renderer/*"], "@main/*": ["src/main/*"]}}}