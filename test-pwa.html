<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PWA 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-item {
            background: #f5f5f5;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #4CAF50;
        }
        .success {
            color: #4CAF50;
        }
        .error {
            color: #f44336;
        }
        .info {
            color: #2196F3;
        }
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #45a049;
        }
    </style>
</head>
<body>
    <h1>🎵 随心听 PWA 测试页面</h1>
    
    <div class="test-item">
        <h3>📱 设备信息</h3>
        <p id="deviceInfo">检测中...</p>
    </div>
    
    <div class="test-item">
        <h3>🔧 PWA 支持检测</h3>
        <p id="pwaSupport">检测中...</p>
    </div>
    
    <div class="test-item">
        <h3>📄 Manifest 检测</h3>
        <p id="manifestCheck">检测中...</p>
    </div>
    
    <div class="test-item">
        <h3>⚙️ Service Worker 检测</h3>
        <p id="swCheck">检测中...</p>
    </div>
    
    <div class="test-item">
        <h3>🍎 iOS Safari 特性</h3>
        <p id="iosCheck">检测中...</p>
    </div>
    
    <div class="test-item">
        <h3>🎯 PWA 模式检测</h3>
        <p id="pwaMode">检测中...</p>
    </div>
    
    <div class="test-item">
        <h3>🔗 操作</h3>
        <button onclick="testInstallPrompt()">测试安装提示</button>
        <button onclick="testNotification()">测试通知</button>
        <button onclick="testOffline()">测试离线页面</button>
        <button onclick="window.location.reload()">刷新页面</button>
    </div>

    <script>
        // 设备信息检测
        function checkDeviceInfo() {
            const info = {
                userAgent: navigator.userAgent,
                platform: navigator.platform,
                language: navigator.language,
                cookieEnabled: navigator.cookieEnabled,
                onLine: navigator.onLine,
                screenSize: `${screen.width}x${screen.height}`,
                viewportSize: `${window.innerWidth}x${window.innerHeight}`
            };
            
            document.getElementById('deviceInfo').innerHTML = `
                <strong>用户代理:</strong> ${info.userAgent}<br>
                <strong>平台:</strong> ${info.platform}<br>
                <strong>语言:</strong> ${info.language}<br>
                <strong>在线状态:</strong> <span class="${info.onLine ? 'success' : 'error'}">${info.onLine ? '在线' : '离线'}</span><br>
                <strong>屏幕尺寸:</strong> ${info.screenSize}<br>
                <strong>视口尺寸:</strong> ${info.viewportSize}
            `;
        }

        // PWA 支持检测
        function checkPWASupport() {
            const support = {
                serviceWorker: 'serviceWorker' in navigator,
                pushManager: 'PushManager' in window,
                notification: 'Notification' in window,
                manifest: 'manifest' in document.createElement('link'),
                standalone: window.matchMedia('(display-mode: standalone)').matches
            };
            
            document.getElementById('pwaSupport').innerHTML = `
                <strong>Service Worker:</strong> <span class="${support.serviceWorker ? 'success' : 'error'}">${support.serviceWorker ? '✅ 支持' : '❌ 不支持'}</span><br>
                <strong>推送管理:</strong> <span class="${support.pushManager ? 'success' : 'error'}">${support.pushManager ? '✅ 支持' : '❌ 不支持'}</span><br>
                <strong>通知:</strong> <span class="${support.notification ? 'success' : 'error'}">${support.notification ? '✅ 支持' : '❌ 不支持'}</span><br>
                <strong>Manifest:</strong> <span class="${support.manifest ? 'success' : 'error'}">${support.manifest ? '✅ 支持' : '❌ 不支持'}</span><br>
                <strong>独立模式:</strong> <span class="${support.standalone ? 'success' : 'info'}">${support.standalone ? '✅ 已安装' : 'ℹ️ 未安装'}</span>
            `;
        }

        // Manifest 检测
        async function checkManifest() {
            try {
                const response = await fetch('/manifest.json');
                const manifest = await response.json();
                
                document.getElementById('manifestCheck').innerHTML = `
                    <strong>应用名称:</strong> ${manifest.name}<br>
                    <strong>短名称:</strong> ${manifest.short_name}<br>
                    <strong>显示模式:</strong> ${manifest.display}<br>
                    <strong>主题颜色:</strong> <span style="color: ${manifest.theme_color}">${manifest.theme_color}</span><br>
                    <strong>图标数量:</strong> ${manifest.icons ? manifest.icons.length : 0}<br>
                    <strong>快捷方式:</strong> ${manifest.shortcuts ? manifest.shortcuts.length : 0}
                `;
            } catch (error) {
                document.getElementById('manifestCheck').innerHTML = `
                    <span class="error">❌ 无法加载 manifest.json: ${error.message}</span>
                `;
            }
        }

        // Service Worker 检测
        function checkServiceWorker() {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    if (registrations.length > 0) {
                        const sw = registrations[0];
                        document.getElementById('swCheck').innerHTML = `
                            <span class="success">✅ Service Worker 已注册</span><br>
                            <strong>作用域:</strong> ${sw.scope}<br>
                            <strong>状态:</strong> ${sw.active ? sw.active.state : '未激活'}
                        `;
                    } else {
                        document.getElementById('swCheck').innerHTML = `
                            <span class="error">❌ Service Worker 未注册</span>
                        `;
                    }
                });
            } else {
                document.getElementById('swCheck').innerHTML = `
                    <span class="error">❌ 浏览器不支持 Service Worker</span>
                `;
            }
        }

        // iOS Safari 检测
        function checkiOS() {
            const isIOS = /iPad|iPhone|iPod/.test(navigator.userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
            const isIOSSafari = isIOS && isSafari;
            const standalone = (window.navigator as any).standalone;
            
            document.getElementById('iosCheck').innerHTML = `
                <strong>iOS 设备:</strong> <span class="${isIOS ? 'success' : 'info'}">${isIOS ? '✅ 是' : 'ℹ️ 否'}</span><br>
                <strong>Safari 浏览器:</strong> <span class="${isSafari ? 'success' : 'info'}">${isSafari ? '✅ 是' : 'ℹ️ 否'}</span><br>
                <strong>iOS Safari:</strong> <span class="${isIOSSafari ? 'success' : 'info'}">${isIOSSafari ? '✅ 是' : 'ℹ️ 否'}</span><br>
                <strong>独立模式:</strong> <span class="${standalone ? 'success' : 'info'}">${standalone ? '✅ 已添加到主屏幕' : 'ℹ️ 未添加到主屏幕'}</span>
            `;
        }

        // PWA 模式检测
        function checkPWAMode() {
            const displayMode = window.matchMedia('(display-mode: standalone)').matches;
            const standalone = (window.navigator as any).standalone;
            const isPWA = displayMode || standalone;
            
            document.getElementById('pwaMode').innerHTML = `
                <strong>当前模式:</strong> <span class="${isPWA ? 'success' : 'info'}">${isPWA ? '✅ PWA 模式' : 'ℹ️ 浏览器模式'}</span><br>
                <strong>显示模式:</strong> ${displayMode ? 'standalone' : 'browser'}<br>
                <strong>iOS 独立模式:</strong> ${standalone ? '是' : '否'}
            `;
        }

        // 测试安装提示
        function testInstallPrompt() {
            if ('beforeinstallprompt' in window) {
                alert('浏览器支持安装提示事件');
            } else {
                alert('浏览器不支持安装提示事件（可能是 iOS Safari）');
            }
        }

        // 测试通知
        function testNotification() {
            if ('Notification' in window) {
                Notification.requestPermission().then(permission => {
                    if (permission === 'granted') {
                        new Notification('随心听', {
                            body: '通知功能正常工作！',
                            icon: '/icons/icon-192x192.png'
                        });
                    }
                });
            } else {
                alert('浏览器不支持通知功能');
            }
        }

        // 测试离线页面
        function testOffline() {
            window.open('/offline.html', '_blank');
        }

        // 初始化检测
        window.addEventListener('load', () => {
            checkDeviceInfo();
            checkPWASupport();
            checkManifest();
            checkServiceWorker();
            checkiOS();
            checkPWAMode();
        });
    </script>
</body>
</html>
