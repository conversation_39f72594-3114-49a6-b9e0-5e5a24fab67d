import axios from 'axios';
import { NETEASE_API } from '@/config/api';

/**
 * 获取音乐URL（优先使用自定义接口）
 */
export const getMusicUrl = async (id: number, br: boolean = false) => {
  try {
    const customApiUrl = `https://music.521668.xyz/jsmusic/music?id=${id}`;
    console.log('🎵 尝试使用自定义接口获取音乐URL:', customApiUrl);

    try {
      const customResponse = await axios.get(customApiUrl, {
        timeout: 5000,
        headers: {
          'User-Agent': 'AlgerMusicPlayer/4.8.1'
        }
      });

      if (customResponse?.data?.url) {
        console.log('✅ 自定义接口获取URL成功');
        return {
          data: {
            data: [
              {
                id,
                url: customResponse.data.url,
                br: 320000,
                size: customResponse.data.size || 0,
                md5: customResponse.data.md5 || '',
                type: 'mp3',
                level: 'standard',
                encodeType: 'mp3'
              }
            ],
            code: 200
          }
        };
      } else {
        console.warn('⚠️ 自定义接口未返回有效URL，将使用原接口');
      }
    } catch (error) {
      console.error('❌ 自定义接口请求失败，使用原接口:', error);
    }

    const level = br ? 'lossless' : 'exhigh';
    const fallbackUrl = `${NETEASE_API}/song/url/v1?id=${id}&level=${level}`;
    const response = await axios.get(fallbackUrl);
    return response;
  } catch (error) {
    console.error('❌ 获取音乐URL失败:', error);
    throw error;
  }
};

/**
 * 获取音乐歌词
 */
export const getMusicLrc = async (id: number) => {
  try {
    const url = `${NETEASE_API}/lyric?id=${id}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error('❌ 获取歌词失败:', error);
    throw error;
  }
};

/**
 * 获取解析后的音乐URL（GD音乐台）
 */
export const getParsingMusicUrl = async (id: number, data: any) => {
  try {
    const { parseFromGDMusic } = await import('./gdmusic');
    const result = await parseFromGDMusic(id, data);
    return result;
  } catch (error) {
    console.error('❌ 解析音乐URL失败:', error);
    throw error;
  }
};

/**
 * 喜欢/取消喜欢歌曲
 */
export const likeSong = async (id: number, like: boolean = true) => {
  try {
    const url = `${NETEASE_API}/like?id=${id}&like=${like}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error('❌ 喜欢/取消喜欢歌曲失败:', error);
    throw error;
  }
};

/**
 * 获取喜欢的歌曲列表
 */
export const getLikedList = async (uid: number) => {
  try {
    const url = `${NETEASE_API}/likelist?uid=${uid}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error('❌ 获取喜欢的歌曲列表失败:', error);
    throw error;
  }
};

/**
 * 获取歌曲详情
 */
export const getMusicDetail = async (id: number) => {
  try {
    const url = `${NETEASE_API}/song/detail?ids=${id}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error('❌ 获取歌曲详情失败:', error);
    throw error;
  }
};

/**
 * 根据类型获取音乐列表
 */
export const getMusicListByType = async (type: string) => {
  try {
    const url = `${NETEASE_API}/playlist/${type}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error(`❌ 获取音乐列表失败（类型：${type}）:`, error);
    throw error;
  }
};

/**
 * 更新歌单中的歌曲（添加或删除）
 */
export const updatePlaylistTracks = async (pid: number, tracks: string, op: 'add' | 'del') => {
  try {
    const url = `${NETEASE_API}/playlist/tracks?op=${op}&pid=${pid}&tracks=${tracks}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error(`❌ 歌单更新失败（op=${op}）:`, error);
    throw error;
  }
};

/**
 * 收藏/取消收藏歌单
 */
export const subscribePlaylist = async (id: number, t: 1 | 2) => {
  try {
    const url = `${NETEASE_API}/playlist/subscribe?t=${t}&id=${id}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error(`❌ 歌单订阅操作失败（t=${t}）:`, error);
    throw error;
  }
};

/**
 * 创建歌单
 * @param name 歌单名称
 * @param privacy 是否私密（0 公开，10 私密）
 * @returns 创建结果
 */
export const createPlaylist = async (name: string, privacy: 0 | 10 = 0) => {
  try {
    const url = `${NETEASE_API}/playlist/create?name=${encodeURIComponent(name)}&privacy=${privacy}`;
    const response = await axios.get(url);
    return response;
  } catch (error) {
    console.error('❌ 创建歌单失败:', error);
    throw error;
  }
};