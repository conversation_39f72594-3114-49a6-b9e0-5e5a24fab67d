declare module '@vueuse/core' {
  export function useThrottleFn<T extends Function>(fn: T, ms?: number): T;
  export function useDebounceFn<T extends Function>(fn: T, ms?: number): T;
  export function useWindowSize(): {
    width: import('vue').Ref<number>;
    height: import('vue').Ref<number>;
  };
  export function useDateFormat(date: Date | number | string, format: string): import('vue').ComputedRef<string>;
  export function useLocalStorage<T>(key: string, defaultValue: T): import('vue').Ref<T>;
} 