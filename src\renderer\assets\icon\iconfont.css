@font-face {
  font-family: 'iconfont'; /* Project id 2685283 */
  src:
    url('iconfont.woff2?t=1703643214551') format('woff2'),
    url('iconfont.woff?t=1703643214551') format('woff'),
    url('iconfont.ttf?t=1703643214551') format('truetype');
}

.iconfont {
  font-family: 'iconfont' !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-list:before {
  content: '\e603';
}

.icon-maxsize:before {
  content: '\e692';
}

.icon-close:before {
  content: '\e616';
}

.icon-minisize:before {
  content: '\e602';
}

.icon-shuaxin:before {
  content: '\e627';
}

.icon-icon_error:before {
  content: '\e615';
}

.icon-a-3User:before {
  content: '\e601';
}

.icon-Chat:before {
  content: '\e605';
}

.icon-Category:before {
  content: '\e606';
}

.icon-Document:before {
  content: '\e607';
}

.icon-Heart:before {
  content: '\e608';
}

.icon-Hide:before {
  content: '\e609';
}

.icon-Home:before {
  content: '\e60a';
}

.icon-a-Image2:before {
  content: '\e60b';
}

.icon-Profile:before {
  content: '\e60c';
}

.icon-Search:before {
  content: '\e60d';
}

.icon-Paper:before {
  content: '\e60e';
}

.icon-Play:before {
  content: '\e60f';
}

.icon-Setting:before {
  content: '\e610';
}

.icon-a-TicketStar:before {
  content: '\e611';
}

.icon-a-VolumeOff:before {
  content: '\e612';
}

.icon-a-VolumeUp:before {
  content: '\e613';
}

.icon-a-VolumeDown:before {
  content: '\e614';
}

.icon-stop:before {
  content: '\e600';
}

.icon-next:before {
  content: '\e6a9';
}

.icon-prev:before {
  content: '\e6ac';
}

.icon-play:before {
  content: '\e6aa';
}

.icon-xiasanjiaoxing:before {
  content: '\e642';
}

.icon-videofill:before {
  content: '\e7c7';
}

.icon-favorfill:before {
  content: '\e64b';
}

.icon-favor:before {
  content: '\e64c';
}

.icon-loading:before {
  content: '\e64f';
}

.icon-search:before {
  content: '\e65c';
}

.icon-likefill:before {
  content: '\e668';
}

.icon-like:before {
  content: '\e669';
}

.icon-notificationfill:before {
  content: '\e66a';
}

.icon-notification:before {
  content: '\e66b';
}

.icon-evaluate:before {
  content: '\e672';
}

.icon-homefill:before {
  content: '\e6bb';
}

.icon-link:before {
  content: '\e6bf';
}

.icon-roundaddfill:before {
  content: '\e6d8';
}

.icon-roundadd:before {
  content: '\e6d9';
}

.icon-add:before {
  content: '\e6da';
}

.icon-appreciatefill:before {
  content: '\e6e3';
}

.icon-forwardfill:before {
  content: '\e6ea';
}

.icon-voicefill:before {
  content: '\e6f0';
}

.icon-wefill:before {
  content: '\e6f4';
}

.icon-keyboard:before {
  content: '\e71b';
}

.icon-picfill:before {
  content: '\e72c';
}

.icon-markfill:before {
  content: '\e730';
}

.icon-presentfill:before {
  content: '\e732';
}

.icon-peoplefill:before {
  content: '\e735';
}

.icon-read:before {
  content: '\e742';
}

.icon-backwardfill:before {
  content: '\e74d';
}

.icon-playfill:before {
  content: '\e74f';
}

.icon-all:before {
  content: '\e755';
}

.icon-hotfill:before {
  content: '\e757';
}

.icon-recordfill:before {
  content: '\e7a4';
}

.icon-full:before {
  content: '\e7bc';
}

.icon-favor_fill_light:before {
  content: '\e7ec';
}

.icon-round_favor_fill:before {
  content: '\e80a';
}

.icon-round_location_fill:before {
  content: '\e80b';
}

.icon-round_like_fill:before {
  content: '\e80c';
}

.icon-round_people_fill:before {
  content: '\e80d';
}

.icon-round_skin_fill:before {
  content: '\e80e';
}

.icon-broadcast_fill:before {
  content: '\e81d';
}

.icon-card_fill:before {
  content: '\e81f';
}
