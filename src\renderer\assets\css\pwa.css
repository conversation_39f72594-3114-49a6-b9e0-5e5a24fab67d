/* PWA 模式专用样式 */

/* iOS Safari PWA 模式优化 */
.pwa-mode {
  /* 确保内容填满整个屏幕 */
  height: 100vh;
  overflow: hidden;
}

/* 适配 iPhone X 系列的安全区域 */
@supports (padding: max(0px)) {
  .pwa-mode {
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}

/* PWA 模式下的状态栏适配 */
.pwa-mode body {
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: hidden;
  -webkit-overflow-scrolling: touch;
}

/* PWA 模式下的主容器 */
.pwa-mode #app {
  height: 100vh;
  overflow: hidden;
  position: relative;
}

/* 防止iOS Safari的橡皮筋效果 */
.pwa-mode body {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* PWA 模式下的滚动容器 */
.pwa-mode .scroll-container {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: none;
}

/* 优化触摸反馈 */
.pwa-mode button,
.pwa-mode .clickable {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
}

/* PWA 模式下的导航栏适配 */
.pwa-mode .navbar {
  padding-top: env(safe-area-inset-top);
  background: var(--bg-color, #ffffff);
}

/* PWA 模式下的底部播放器适配 */
.pwa-mode .player-bar {
  padding-bottom: env(safe-area-inset-bottom);
  background: var(--bg-color, #ffffff);
}

/* 全屏模式下的特殊处理 */
.pwa-mode.fullscreen {
  padding: 0 !important;
}

/* 横屏模式适配 */
@media screen and (orientation: landscape) {
  .pwa-mode {
    padding-left: max(20px, env(safe-area-inset-left));
    padding-right: max(20px, env(safe-area-inset-right));
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pwa-mode {
    background-color: #1a1a1a;
    color: #ffffff;
  }
  
  .pwa-mode .navbar,
  .pwa-mode .player-bar {
    background: #1a1a1a;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2) {
  .pwa-mode {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}

/* PWA 启动动画 */
.pwa-mode .app-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: var(--primary-color, #4CAF50);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  opacity: 1;
  transition: opacity 0.5s ease-out;
}

.pwa-mode .app-loading.fade-out {
  opacity: 0;
  pointer-events: none;
}

.pwa-mode .app-loading .logo {
  width: 80px;
  height: 80px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 防止文本选择 */
.pwa-mode {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 允许输入框文本选择 */
.pwa-mode input,
.pwa-mode textarea {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* PWA 模式下的特殊按钮样式 */
.pwa-mode .pwa-install-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--primary-color, #4CAF50);
  color: white;
  border: none;
  border-radius: 50px;
  padding: 12px 20px;
  font-size: 14px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  display: none;
}

.pwa-mode .pwa-install-button.show {
  display: block;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 状态栏颜色适配 */
.pwa-mode[data-theme="light"] {
  --status-bar-color: #ffffff;
}

.pwa-mode[data-theme="dark"] {
  --status-bar-color: #1a1a1a;
}

/* 确保内容不被状态栏遮挡 */
.pwa-mode .main-content {
  padding-top: env(safe-area-inset-top);
  height: calc(100vh - env(safe-area-inset-top) - env(safe-area-inset-bottom));
  overflow-y: auto;
}

/* iOS 特定的滚动优化 */
.pwa-mode .scrollable {
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

/* 防止页面弹跳 */
.pwa-mode body {
  overscroll-behavior: none;
}
