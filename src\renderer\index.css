/* ./src/index.css */

/*! @import */
@tailwind base;
@tailwind components;
@tailwind utilities;

.n-image img {
  background-color: #111111;
  width: 100%;
}

.n-slider-handle-indicator--top {
  @apply bg-transparent text-2xl px-2 py-1 shadow-none mb-0 text-white bg-dark-300 dark:bg-gray-800 bg-opacity-80 rounded-lg  !important;
  mix-blend-mode: difference !important;
}

.v-binder-follower-container:has(.n-slider-handle-indicator--top) {
  z-index: 999999999 !important;
}

.text-el {
  @apply overflow-ellipsis overflow-hidden whitespace-nowrap;
}

.theme-dark {
  --bg-color: #000;
  --text-color: #fff;
  --bg-color-100: #161616;
  --bg-color-200: #2d2d2d;
  --bg-color-300: #3d3d3d;
  --text-color: #f8f9fa;
  --text-color-100: #e9ecef;
  --text-color-200: #dee2e6;
  --text-color-300: #dde0e3;
  --primary-color: #22c55e;
}

.theme-light {
  --bg-color: #fff;
  --text-color: #000;
  --bg-color-100: #f8f9fa;
  --bg-color-200: #e9ecef;
  --bg-color-300: #dee2e6;
  --text-color: #000;
  --text-color-100: #161616;
  --text-color-200: #2d2d2d;
  --text-color-300: #3d3d3d;
  --primary-color: #22c55e;
}

.theme-gray {
  --bg-color: #f8f9fa;
  --text-color: #000;
  --bg-color-100: #e9ecef;
  --bg-color-200: #dee2e6;
  --bg-color-300: #dde0e3;
  --text-color: #000;
  --text-color-100: #161616;
  --text-color-200: #2d2d2d;
  --text-color-300: #3d3d3d;
  --primary-color: #22c55e;
}

:root {
  --text-color: #000000dd;
}

:root[class='dark'] {
  --text-color: #ffffffdd;
}
