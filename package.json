{"name": "随心听-随时随地，好音乐不等待", "version": "4.8.1", "description": "随心听-随时随地，好音乐不等待", "author": "Alger <<EMAIL>>", "main": "./out/main/index.js", "homepage": "https://github.com/algerkong/AlgerMusicPlayer", "scripts": {"prepare": "husky", "format": "prettier --write .", "lint": "eslint . --ext .js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "start": "electron-vite preview", "dev": "electron-vite dev", "dev:web": "vite dev", "build": "electron-vite build", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux"}, "dependencies": {"@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^4.0.0", "@unblockneteasemusic/server": "^0.27.8-patch.1", "cors": "^2.8.5", "electron-store": "^8.1.0", "electron-updater": "^6.6.2", "electron-window-state": "^5.0.3", "express": "^4.18.2", "file-type": "^21.0.0", "font-list": "^1.5.1", "husky": "^9.1.7", "music-metadata": "^11.2.3", "netease-cloud-music-api-alger": "^4.26.1", "node-id3": "^0.2.9", "node-machine-id": "^1.1.12", "vue-i18n": "^11.1.3"}, "devDependencies": {"@electron-toolkit/eslint-config": "^2.1.0", "@electron-toolkit/eslint-config-ts": "^3.1.0", "@electron-toolkit/tsconfig": "^1.0.1", "@rushstack/eslint-patch": "^1.10.3", "@tailwindcss/postcss7-compat": "^2.2.4", "@types/howler": "^2.2.12", "@types/node": "^20.14.8", "@types/tinycolor2": "^1.4.6", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "@vitejs/plugin-vue": "^5.0.5", "@vue/compiler-sfc": "^3.5.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/runtime-core": "^3.5.0", "@vueuse/core": "^11.3.0", "@vueuse/electron": "^11.3.0", "animate.css": "^4.1.1", "autoprefixer": "^10.4.20", "axios": "^1.7.7", "cross-env": "^7.0.3", "electron": "^35.2.0", "electron-builder": "^25.1.8", "electron-vite": "^3.1.0", "eslint": "^9.0.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-import": "^2.29.1", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-simple-import-sort": "^12.0.0", "eslint-plugin-vue": "^10.0.0", "eslint-plugin-vue-scoped-css": "^2.9.0", "howler": "^2.2.4", "lodash": "^4.17.21", "marked": "^15.0.4", "naive-ui": "^2.41.0", "pinia": "^3.0.1", "pinyin-match": "^1.2.6", "postcss": "^8.5.3", "prettier": "^3.3.2", "remixicon": "^4.6.0", "sass": "^1.86.0", "tailwindcss": "^3.4.17", "tinycolor2": "^1.6.0", "tunajs": "^1.0.15", "typescript": "^5.5.2", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "vite": "^6.2.2", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "7.7.2", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-tsc": "^2.0.22"}, "build": {"appId": "com.alger.music", "productName": "随心听-随时随地，好音乐不等待", "publish": [{"provider": "github", "owner": "algerkong", "repo": "AlgerMusicPlayer"}], "extraResources": [{"from": "resources/html", "to": "html", "filter": ["**/*"]}], "mac": {"icon": "resources/icon.icns", "target": [{"target": "dmg", "arch": ["universal"]}], "artifactName": "${productName}-${version}-mac-${arch}.${ext}", "darkModeSupport": true, "hardenedRuntime": false, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist", "notarize": false, "identity": null, "type": "distribution", "binaries": ["Contents/MacOS/随心听-随时随地，好音乐不等待"]}, "win": {"icon": "resources/icon.ico", "target": [{"target": "nsis", "arch": ["x64", "ia32", "arm64"]}], "artifactName": "${productName}-${version}-win-${arch}.${ext}", "requestedExecutionLevel": "asInvoker"}, "linux": {"icon": "resources/icon.png", "target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}], "artifactName": "${productName}-${version}-linux-${arch}.${ext}", "category": "Audio", "maintainer": "Alger <<EMAIL>>"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "installerIcon": "resources/icon.ico", "uninstallerIcon": "resources/icon.ico", "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "随心听-随时随地，好音乐不等待", "include": "build/installer.nsh"}}}