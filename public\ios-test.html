<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover, shrink-to-fit=no">
    <title>iOS Safari 地址栏隐藏测试</title>
    
    <!-- iOS Safari 专用配置 -->
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="地址栏测试">
    <meta name="apple-touch-fullscreen" content="yes">
    <meta name="format-detection" content="telephone=no, date=no, address=no, email=no">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        
        html, body {
            height: 100vh;
            height: 100dvh;
            width: 100vw;
            position: fixed;
            top: 0;
            left: 0;
            overflow: hidden;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            -webkit-overflow-scrolling: auto;
            overscroll-behavior: none;
        }
        
        .container {
            height: 100vh;
            height: 100dvh;
            width: 100vw;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .title {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
        }
        
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            width: 100%;
            max-width: 400px;
            margin-bottom: 30px;
        }
        
        .info-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .info-label {
            font-size: 12px;
            opacity: 0.8;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-size: 18px;
            font-weight: bold;
        }
        
        .status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            width: 100%;
            max-width: 400px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .status-item:last-child {
            margin-bottom: 0;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #4CAF50;
        }
        
        .status-indicator.error {
            background: #f44336;
        }
        
        .status-indicator.warning {
            background: #ff9800;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 15px;
            width: 100%;
            max-width: 300px;
        }
        
        .button {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }
        
        .button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .log {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            max-height: 150px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            padding: 15px;
            font-size: 12px;
            font-family: monospace;
            overflow-y: auto;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .log-entry {
            margin-bottom: 5px;
            opacity: 0.9;
        }
        
        .log-entry:last-child {
            margin-bottom: 0;
        }
        
        /* iOS 专用样式 */
        @supports (-webkit-touch-callout: none) {
            html, body {
                position: fixed !important;
                overflow: hidden !important;
                height: 100% !important;
                width: 100% !important;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="title">🍎 iOS Safari 测试</div>
            <div class="subtitle">地址栏隐藏效果验证</div>
        </div>
        
        <div class="info-grid">
            <div class="info-card">
                <div class="info-label">屏幕高度</div>
                <div class="info-value" id="screenHeight">-</div>
            </div>
            <div class="info-card">
                <div class="info-label">视口高度</div>
                <div class="info-value" id="viewportHeight">-</div>
            </div>
            <div class="info-card">
                <div class="info-label">滚动位置</div>
                <div class="info-value" id="scrollPosition">-</div>
            </div>
            <div class="info-card">
                <div class="info-label">设备像素比</div>
                <div class="info-value" id="devicePixelRatio">-</div>
            </div>
        </div>
        
        <div class="status">
            <div class="status-item">
                <span>iOS 设备</span>
                <div class="status-indicator" id="iosStatus"></div>
            </div>
            <div class="status-item">
                <span>Safari 浏览器</span>
                <div class="status-indicator" id="safariStatus"></div>
            </div>
            <div class="status-item">
                <span>PWA 模式</span>
                <div class="status-indicator" id="pwaStatus"></div>
            </div>
            <div class="status-item">
                <span>地址栏隐藏</span>
                <div class="status-indicator" id="addressBarStatus"></div>
            </div>
        </div>
        
        <div class="buttons">
            <button class="button" onclick="hideAddressBar()">🔄 隐藏地址栏</button>
            <button class="button" onclick="testFullscreen()">📱 测试全屏</button>
            <button class="button" onclick="clearLog()">🗑️ 清除日志</button>
            <button class="button" onclick="window.location.href='/'">🏠 返回主页</button>
        </div>
    </div>
    
    <div class="log" id="log"></div>
    
    <script>
        // 日志功能
        function log(message) {
            const logElement = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log-entry';
            entry.textContent = `${new Date().toLocaleTimeString()}: ${message}`;
            logElement.appendChild(entry);
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        // 设备检测
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const isIOS = /iPad|iPhone|iPod/.test(userAgent);
            const isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);
            const isPWA = window.matchMedia('(display-mode: standalone)').matches || 
                          window.navigator.standalone === true;
            
            document.getElementById('iosStatus').className = 
                'status-indicator ' + (isIOS ? '' : 'error');
            document.getElementById('safariStatus').className = 
                'status-indicator ' + (isSafari ? '' : 'error');
            document.getElementById('pwaStatus').className = 
                'status-indicator ' + (isPWA ? '' : 'warning');
            
            log(`设备检测: iOS=${isIOS}, Safari=${isSafari}, PWA=${isPWA}`);
            
            return { isIOS, isSafari, isPWA };
        }
        
        // 更新视口信息
        function updateViewportInfo() {
            document.getElementById('screenHeight').textContent = screen.height + 'px';
            document.getElementById('viewportHeight').textContent = window.innerHeight + 'px';
            document.getElementById('scrollPosition').textContent = window.pageYOffset + 'px';
            document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
            
            // 检查地址栏是否隐藏
            const heightDiff = screen.height - window.innerHeight;
            const isHidden = heightDiff < 100; // 如果差值小于100px，认为地址栏已隐藏
            
            document.getElementById('addressBarStatus').className = 
                'status-indicator ' + (isHidden ? '' : 'error');
            
            log(`视口更新: 屏幕=${screen.height}, 视口=${window.innerHeight}, 差值=${heightDiff}`);
        }
        
        // 地址栏隐藏功能
        function hideAddressBar() {
            log('开始执行地址栏隐藏...');
            
            const device = detectDevice();
            if (device.isPWA) {
                log('PWA模式，无需隐藏地址栏');
                return;
            }
            
            // 多重隐藏方法
            const methods = [
                () => {
                    window.scrollTo(0, 1);
                    log('执行: scrollTo(0, 1)');
                },
                () => {
                    requestAnimationFrame(() => {
                        window.scrollTo(0, 1);
                        log('执行: RAF scrollTo(0, 1)');
                    });
                },
                () => {
                    setTimeout(() => {
                        window.scrollTo(0, 0);
                        log('执行: scrollTo(0, 0)');
                    }, 50);
                },
                () => {
                    window.dispatchEvent(new Event('resize'));
                    log('执行: 触发resize事件');
                },
                () => {
                    if (window.visualViewport) {
                        const vh = window.visualViewport.height;
                        document.documentElement.style.setProperty('--vh', vh + 'px');
                        log(`执行: 设置--vh=${vh}px`);
                    }
                }
            ];
            
            // 执行所有方法
            methods.forEach((method, index) => {
                setTimeout(method, index * 100);
            });
            
            // 延迟更新视口信息
            setTimeout(updateViewportInfo, 1000);
        }
        
        // 测试全屏
        function testFullscreen() {
            log('测试全屏模式...');
            
            // 应用全屏样式
            document.documentElement.style.position = 'fixed';
            document.documentElement.style.width = '100vw';
            document.documentElement.style.height = '100vh';
            document.documentElement.style.overflow = 'hidden';
            
            document.body.style.position = 'fixed';
            document.body.style.width = '100vw';
            document.body.style.height = '100vh';
            document.body.style.overflow = 'hidden';
            
            log('已应用全屏样式');
            setTimeout(updateViewportInfo, 500);
        }
        
        // 初始化
        function init() {
            log('页面初始化...');
            detectDevice();
            updateViewportInfo();
            
            // 自动尝试隐藏地址栏
            setTimeout(hideAddressBar, 1000);
        }
        
        // 事件监听
        window.addEventListener('load', init);
        window.addEventListener('resize', updateViewportInfo);
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                log('方向变化，重新检测');
                updateViewportInfo();
                hideAddressBar();
            }, 500);
        });
        
        if (window.visualViewport) {
            window.visualViewport.addEventListener('resize', updateViewportInfo);
        }
        
        // 定期更新
        setInterval(updateViewportInfo, 5000);
    </script>
</body>
</html>
