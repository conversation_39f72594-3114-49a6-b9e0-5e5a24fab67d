/**
 * PWA 相关工具函数
 */

// 检查是否在PWA模式下运行
export function isPWAMode(): boolean {
  return (
    window.matchMedia('(display-mode: standalone)').matches ||
    (window.navigator as any).standalone === true ||
    document.referrer.includes('android-app://')
  )
}

// 检查是否为移动设备
export function isMobileDevice(): boolean {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
    navigator.userAgent
  )
}

// 检查是否为iOS设备
export function isIOSDevice(): boolean {
  return /iPad|iPhone|iPod/.test(navigator.userAgent)
}

// 检查是否为Safari浏览器
export function isSafari(): boolean {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
}

// 检查是否为iOS Safari
export function isIOSSafari(): boolean {
  return isIOSDevice() && isSafari()
}

// 检查是否支持PWA安装
export function supportsPWAInstall(): boolean {
  return 'serviceWorker' in navigator && 'PushManager' in window
}

// 获取设备类型
export function getDeviceType(): 'mobile' | 'tablet' | 'desktop' {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (/ipad|android(?!.*mobile)|tablet/i.test(userAgent)) {
    return 'tablet'
  }
  
  if (/mobile|iphone|ipod|android.*mobile|blackberry|opera mini|iemobile/i.test(userAgent)) {
    return 'mobile'
  }
  
  return 'desktop'
}

// 获取操作系统
export function getOperatingSystem(): string {
  const userAgent = navigator.userAgent.toLowerCase()
  
  if (/iphone|ipad|ipod/.test(userAgent)) {
    return 'iOS'
  }
  
  if (/android/.test(userAgent)) {
    return 'Android'
  }
  
  if (/windows/.test(userAgent)) {
    return 'Windows'
  }
  
  if (/macintosh|mac os x/.test(userAgent)) {
    return 'macOS'
  }
  
  if (/linux/.test(userAgent)) {
    return 'Linux'
  }
  
  return 'Unknown'
}

// 添加PWA相关的CSS类
export function addPWAClasses(): void {
  const html = document.documentElement
  
  // 添加PWA模式类
  if (isPWAMode()) {
    html.classList.add('pwa-mode')
  }
  
  // 添加设备类型类
  html.classList.add(`device-${getDeviceType()}`)
  
  // 添加操作系统类
  html.classList.add(`os-${getOperatingSystem().toLowerCase()}`)
  
  // 添加浏览器类
  if (isSafari()) {
    html.classList.add('browser-safari')
  }
  
  if (isIOSSafari()) {
    html.classList.add('ios-safari')
  }
}

// 设置状态栏样式（iOS Safari）
export function setStatusBarStyle(style: 'default' | 'black' | 'black-translucent' = 'default'): void {
  if (!isIOSDevice()) return
  
  let metaTag = document.querySelector('meta[name="apple-mobile-web-app-status-bar-style"]')
  
  if (!metaTag) {
    metaTag = document.createElement('meta')
    metaTag.setAttribute('name', 'apple-mobile-web-app-status-bar-style')
    document.head.appendChild(metaTag)
  }
  
  metaTag.setAttribute('content', style)
}

// 设置主题颜色
export function setThemeColor(color: string, darkColor?: string): void {
  // 设置基本主题颜色
  let metaTag = document.querySelector('meta[name="theme-color"]')
  if (!metaTag) {
    metaTag = document.createElement('meta')
    metaTag.setAttribute('name', 'theme-color')
    document.head.appendChild(metaTag)
  }
  metaTag.setAttribute('content', color)
  
  // 设置深色模式主题颜色
  if (darkColor) {
    let darkMetaTag = document.querySelector('meta[name="theme-color"][media="(prefers-color-scheme: dark)"]')
    if (!darkMetaTag) {
      darkMetaTag = document.createElement('meta')
      darkMetaTag.setAttribute('name', 'theme-color')
      darkMetaTag.setAttribute('media', '(prefers-color-scheme: dark)')
      document.head.appendChild(darkMetaTag)
    }
    darkMetaTag.setAttribute('content', darkColor)
  }
}

// 防止iOS Safari的橡皮筋效果
export function preventIOSBounce(): void {
  if (!isIOSDevice()) return
  
  // 防止双击缩放
  let lastTouchEnd = 0
  document.addEventListener('touchend', (event) => {
    const now = Date.now()
    if (now - lastTouchEnd <= 300) {
      event.preventDefault()
    }
    lastTouchEnd = now
  }, false)
  
  // 防止缩放手势
  document.addEventListener('touchmove', (event) => {
    if ((event as any).scale !== 1) {
      event.preventDefault()
    }
  }, { passive: false })
  
  // 防止页面弹跳
  document.addEventListener('touchstart', (event) => {
    if (event.touches.length > 1) {
      event.preventDefault()
    }
  }, { passive: false })
}

// 隐藏Safari地址栏
export function hideSafariAddressBar(): void {
  if (!isIOSSafari()) return

  // 确保在PWA模式下不执行，因为PWA模式本身就是全屏的
  if (isPWAMode()) return

  const hideAddressBar = () => {
    // 使用多种方法确保地址栏隐藏
    if (window.pageYOffset === 0) {
      window.scrollTo(0, 1)
    }

    // 强制触发resize事件
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'))
    }, 100)
  }

  // 页面加载完成后隐藏
  if (document.readyState === 'complete') {
    setTimeout(hideAddressBar, 100)
  } else {
    window.addEventListener('load', () => {
      setTimeout(hideAddressBar, 100)
    })
  }

  // 在用户交互后再次尝试隐藏
  let hasInteracted = false
  const handleFirstInteraction = () => {
    if (!hasInteracted) {
      hasInteracted = true
      setTimeout(hideAddressBar, 50)
      document.removeEventListener('touchstart', handleFirstInteraction)
      document.removeEventListener('click', handleFirstInteraction)
    }
  }

  document.addEventListener('touchstart', handleFirstInteraction, { passive: true })
  document.addEventListener('click', handleFirstInteraction)

  // 监听方向变化
  window.addEventListener('orientationchange', () => {
    setTimeout(hideAddressBar, 500)
  })
}

// 检查网络状态
export function getNetworkStatus(): {
  online: boolean
  type?: string
  effectiveType?: string
} {
  const connection = (navigator as any).connection || 
                    (navigator as any).mozConnection || 
                    (navigator as any).webkitConnection
  
  return {
    online: navigator.onLine,
    type: connection?.type,
    effectiveType: connection?.effectiveType
  }
}

// 监听网络状态变化
export function onNetworkChange(callback: (online: boolean) => void): () => void {
  const handleOnline = () => callback(true)
  const handleOffline = () => callback(false)
  
  window.addEventListener('online', handleOnline)
  window.addEventListener('offline', handleOffline)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('online', handleOnline)
    window.removeEventListener('offline', handleOffline)
  }
}

// 获取安全区域信息
export function getSafeAreaInsets(): {
  top: number
  right: number
  bottom: number
  left: number
} {
  const computedStyle = getComputedStyle(document.documentElement)
  
  return {
    top: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-top)')) || 0,
    right: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-right)')) || 0,
    bottom: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-bottom)')) || 0,
    left: parseInt(computedStyle.getPropertyValue('env(safe-area-inset-left)')) || 0
  }
}

// 初始化PWA功能
export function initPWA(): void {
  // 添加CSS类
  addPWAClasses()
  
  // iOS特殊处理
  if (isIOSDevice()) {
    preventIOSBounce()
    hideSafariAddressBar()
    setStatusBarStyle('default')
  }
  
  // 设置主题颜色
  setThemeColor('#4CAF50', '#2E7D32')
  
  console.log('PWA 初始化完成', {
    isPWA: isPWAMode(),
    device: getDeviceType(),
    os: getOperatingSystem(),
    browser: isSafari() ? 'Safari' : 'Other'
  })
}

// 显示安装提示
export function showInstallPrompt(): Promise<boolean> {
  return new Promise((resolve) => {
    if (isIOSSafari()) {
      // Safari特殊处理
      const result = confirm(
        '要安装随心听到主屏幕：\n' +
        '1. 点击底部的分享按钮 📤\n' +
        '2. 选择"添加到主屏幕"\n' +
        '3. 点击"添加"'
      )
      resolve(result)
    } else {
      // 其他浏览器等待beforeinstallprompt事件
      resolve(false)
    }
  })
}
