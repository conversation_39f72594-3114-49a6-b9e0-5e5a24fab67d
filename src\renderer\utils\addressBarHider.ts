/**
 * 地址栏隐藏工具 - 支持所有浏览器和设备
 * 实现在浏览器模式下自动隐藏地址栏（上方或下方）
 */

// 检测设备和浏览器类型
export function getDeviceInfo() {
  const userAgent = navigator.userAgent;
  
  return {
    isIOS: /iPad|iPhone|iPod/.test(userAgent),
    isAndroid: /Android/.test(userAgent),
    isSafari: /^((?!chrome|android).)*safari/i.test(userAgent),
    isChrome: /Chrome/.test(userAgent) && !/Edge/.test(userAgent),
    isFirefox: /Firefox/.test(userAgent),
    isMobile: /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent),
    isTablet: /iPad|Android(?=.*Mobile)|Tablet/.test(userAgent),
    isPWA: window.matchMedia('(display-mode: standalone)').matches || 
           (window.navigator as any).standalone === true
  };
}

// 获取视口信息
export function getViewportInfo() {
  return {
    width: window.innerWidth,
    height: window.innerHeight,
    screenWidth: screen.width,
    screenHeight: screen.height,
    devicePixelRatio: window.devicePixelRatio || 1,
    orientation: screen.orientation?.angle || 0
  };
}

// 强制全屏模式
export function enableFullscreen() {
  const device = getDeviceInfo();
  
  // 如果已经是PWA模式，不需要处理
  if (device.isPWA) {
    console.log('已在PWA模式，无需隐藏地址栏');
    return;
  }
  
  // 设置基础样式
  document.documentElement.style.height = '100vh';
  document.documentElement.style.overflow = 'hidden';
  document.body.style.height = '100vh';
  document.body.style.margin = '0';
  document.body.style.padding = '0';
  document.body.style.overflow = 'hidden';
  
  // 添加全屏类
  document.documentElement.classList.add('fullscreen-mode');
  document.body.classList.add('fullscreen-mode');
  
  console.log('已启用全屏模式');
}

// iOS Safari 地址栏隐藏
export function hideIOSAddressBar() {
  const device = getDeviceInfo();
  
  if (!device.isIOS || !device.isSafari || device.isPWA) {
    return;
  }
  
  const hideBar = () => {
    // 多种方法组合使用
    if (window.pageYOffset === 0) {
      window.scrollTo(0, 1);
    }
    
    // 强制触发resize
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
      window.scrollTo(0, 0); // 滚动回顶部
    }, 100);
    
    // 再次尝试
    setTimeout(() => {
      if (window.pageYOffset <= 1) {
        window.scrollTo(0, 1);
      }
    }, 300);
  };
  
  // 立即执行
  hideBar();
  
  // 页面加载完成后执行
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', hideBar);
  }
  
  window.addEventListener('load', () => {
    setTimeout(hideBar, 100);
  });
  
  // 用户交互后执行
  let hasInteracted = false;
  const handleInteraction = () => {
    if (!hasInteracted) {
      hasInteracted = true;
      setTimeout(hideBar, 50);
      document.removeEventListener('touchstart', handleInteraction);
      document.removeEventListener('click', handleInteraction);
      document.removeEventListener('scroll', handleInteraction);
    }
  };
  
  document.addEventListener('touchstart', handleInteraction, { passive: true });
  document.addEventListener('click', handleInteraction);
  document.addEventListener('scroll', handleInteraction, { passive: true });
  
  // 方向变化时重新隐藏
  window.addEventListener('orientationchange', () => {
    setTimeout(hideBar, 500);
  });
  
  // 窗口大小变化时重新隐藏
  window.addEventListener('resize', () => {
    setTimeout(hideBar, 200);
  });
  
  console.log('iOS Safari 地址栏隐藏已启用');
}

// Android Chrome 地址栏隐藏
export function hideAndroidAddressBar() {
  const device = getDeviceInfo();
  
  if (!device.isAndroid || device.isPWA) {
    return;
  }
  
  const hideBar = () => {
    // Android Chrome 的地址栏隐藏方法
    window.scrollTo(0, 1);
    
    // 使用requestAnimationFrame确保执行
    requestAnimationFrame(() => {
      window.scrollTo(0, 0);
    });
    
    // 强制触发resize
    setTimeout(() => {
      window.dispatchEvent(new Event('resize'));
    }, 100);
  };
  
  // 立即执行
  hideBar();
  
  // 页面加载完成后执行
  window.addEventListener('load', () => {
    setTimeout(hideBar, 100);
  });
  
  // 滚动时隐藏
  let scrollTimeout: number;
  window.addEventListener('scroll', () => {
    clearTimeout(scrollTimeout);
    scrollTimeout = window.setTimeout(() => {
      if (window.pageYOffset === 0) {
        hideBar();
      }
    }, 100);
  }, { passive: true });
  
  console.log('Android Chrome 地址栏隐藏已启用');
}

// 通用地址栏隐藏方法
export function hideAddressBar() {
  const device = getDeviceInfo();
  
  console.log('设备信息:', device);
  
  // 如果是PWA模式，不需要隐藏地址栏
  if (device.isPWA) {
    console.log('PWA模式，地址栏已隐藏');
    return;
  }
  
  // 启用全屏模式
  enableFullscreen();
  
  // 根据设备类型选择隐藏方法
  if (device.isIOS && device.isSafari) {
    hideIOSAddressBar();
  } else if (device.isAndroid) {
    hideAndroidAddressBar();
  } else {
    // 桌面浏览器或其他移动浏览器
    console.log('桌面浏览器或其他移动浏览器，使用通用方法');
    
    // 通用方法
    const genericHide = () => {
      window.scrollTo(0, 1);
      setTimeout(() => {
        window.scrollTo(0, 0);
        window.dispatchEvent(new Event('resize'));
      }, 100);
    };
    
    genericHide();
    window.addEventListener('load', genericHide);
  }
}

// 监听视口变化
export function watchViewportChanges(callback: (info: any) => void) {
  const updateViewport = () => {
    const info = {
      ...getViewportInfo(),
      ...getDeviceInfo()
    };
    callback(info);
  };
  
  // 立即执行一次
  updateViewport();
  
  // 监听各种变化事件
  window.addEventListener('resize', updateViewport);
  window.addEventListener('orientationchange', () => {
    setTimeout(updateViewport, 500);
  });
  
  // 返回清理函数
  return () => {
    window.removeEventListener('resize', updateViewport);
    window.removeEventListener('orientationchange', updateViewport);
  };
}

// 初始化地址栏隐藏功能
export function initAddressBarHider() {
  console.log('初始化地址栏隐藏功能...');
  
  // 立即尝试隐藏
  hideAddressBar();
  
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      setTimeout(hideAddressBar, 100);
    }
  });
  
  // 监听焦点变化
  window.addEventListener('focus', () => {
    setTimeout(hideAddressBar, 100);
  });
  
  console.log('地址栏隐藏功能初始化完成');
}
