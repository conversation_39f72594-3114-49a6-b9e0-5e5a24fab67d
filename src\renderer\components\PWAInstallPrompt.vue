<template>
  <div v-if="showInstallPrompt" class="pwa-install-prompt">
    <div class="prompt-content">
      <div class="prompt-icon">📱</div>
      <div class="prompt-text">
        <h3>安装随心听</h3>
        <p>将随心听添加到主屏幕，获得更好的使用体验</p>
      </div>
      <div class="prompt-actions">
        <button @click="installPWA" class="install-btn">安装</button>
        <button @click="dismissPrompt" class="dismiss-btn">稍后</button>
      </div>
    </div>
    <button @click="dismissPrompt" class="close-btn">×</button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const showInstallPrompt = ref(false)
let deferredPrompt: any = null

// 检查是否已经安装
const isPWAInstalled = () => {
  return window.matchMedia('(display-mode: standalone)').matches || 
         (window.navigator as any).standalone === true
}

// 检查是否为移动设备
const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 检查是否为Safari
const isSafari = () => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
}

// 处理安装提示事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault()
  deferredPrompt = e
  
  // 如果还没有安装且是移动设备，显示安装提示
  if (!isPWAInstalled() && isMobile()) {
    showInstallPrompt.value = true
  }
}

// 安装PWA
const installPWA = async () => {
  if (deferredPrompt) {
    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('用户接受了PWA安装')
    } else {
      console.log('用户拒绝了PWA安装')
    }
    
    deferredPrompt = null
    showInstallPrompt.value = false
  } else if (isSafari() && isMobile()) {
    // Safari特殊处理
    showSafariInstallInstructions()
  }
}

// 显示Safari安装说明
const showSafariInstallInstructions = () => {
  alert('要安装随心听到主屏幕：\n1. 点击底部的分享按钮 📤\n2. 选择"添加到主屏幕"\n3. 点击"添加"')
}

// 关闭提示
const dismissPrompt = () => {
  showInstallPrompt.value = false
  // 记录用户已经看过提示，避免频繁显示
  localStorage.setItem('pwa-install-dismissed', Date.now().toString())
}

// 检查是否应该显示提示
const shouldShowPrompt = () => {
  const dismissed = localStorage.getItem('pwa-install-dismissed')
  if (dismissed) {
    const dismissedTime = parseInt(dismissed)
    const now = Date.now()
    // 如果距离上次关闭超过7天，再次显示
    return (now - dismissedTime) > 7 * 24 * 60 * 60 * 1000
  }
  return true
}

onMounted(() => {
  // 监听安装提示事件
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
  
  // 对于Safari，检查是否应该显示安装提示
  if (isSafari() && isMobile() && !isPWAInstalled() && shouldShowPrompt()) {
    setTimeout(() => {
      showInstallPrompt.value = true
    }, 3000) // 3秒后显示
  }
})

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
})
</script>

<style scoped>
.pwa-install-prompt {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  animation: slideUp 0.3s ease-out;
  max-width: 400px;
  margin: 0 auto;
}

.prompt-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
}

.prompt-icon {
  font-size: 24px;
  flex-shrink: 0;
}

.prompt-text {
  flex: 1;
  min-width: 0;
}

.prompt-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.prompt-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.prompt-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.install-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.install-btn:hover {
  background: #45a049;
}

.dismiss-btn {
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.dismiss-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #666;
}

@keyframes slideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pwa-install-prompt {
    background: #2d2d2d;
    color: white;
  }
  
  .prompt-text h3 {
    color: white;
  }
  
  .prompt-text p {
    color: #ccc;
  }
  
  .dismiss-btn {
    color: #ccc;
    border-color: #555;
  }
  
  .dismiss-btn:hover {
    background: #3d3d3d;
    border-color: #666;
  }
  
  .close-btn {
    color: #ccc;
  }
  
  .close-btn:hover {
    background: #3d3d3d;
    color: white;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .pwa-install-prompt {
    left: 16px;
    right: 16px;
    bottom: 16px;
  }
  
  .prompt-content {
    padding: 14px 16px;
  }
  
  .prompt-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .install-btn,
  .dismiss-btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* PWA模式下隐藏 */
@media (display-mode: standalone) {
  .pwa-install-prompt {
    display: none;
  }
}
</style>
