<template>
  <div v-if="showInstallPrompt" class="pwa-install-prompt">
    <div class="prompt-content">
      <div class="prompt-icon">
        <!-- 优先使用SVG图标 -->
        <svg class="app-logo" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <linearGradient id="promptNoteGradient1" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#2E7D32;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="promptNoteGradient2" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#673AB7;stop-opacity:1" />
            </linearGradient>
            <linearGradient id="promptLineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
              <stop offset="50%" style="stop-color:#00BCD4;stop-opacity:1" />
              <stop offset="100%" style="stop-color:#9C27B0;stop-opacity:1" />
            </linearGradient>
          </defs>

          <!-- 五线谱线条 -->
          <g stroke="url(#promptLineGradient)" stroke-width="3" opacity="0.8">
            <line x1="20" y1="60" x2="180" y2="50" />
            <line x1="20" y1="80" x2="180" y2="70" />
            <line x1="20" y1="100" x2="180" y2="90" />
            <line x1="20" y1="120" x2="180" y2="110" />
            <line x1="20" y1="140" x2="180" y2="130" />
          </g>

          <!-- 音符1 -->
          <g fill="url(#promptNoteGradient1)">
            <circle cx="50" cy="130" r="12" />
            <rect x="62" y="90" width="3" height="40" />
            <path d="M65 90 Q75 85 80 90 Q75 95 65 100 Z" opacity="0.8"/>
          </g>

          <!-- 音符2 -->
          <g fill="url(#promptNoteGradient2)">
            <circle cx="150" cy="80" r="15" />
            <rect x="165" y="40" width="4" height="40" />
            <path d="M169 40 Q180 35 185 40 Q180 45 169 50 Z" opacity="0.8"/>
          </g>

          <!-- 装饰性小音符 -->
          <g fill="#E0E0E0" opacity="0.6">
            <circle cx="90" cy="110" r="8" />
            <rect x="98" y="85" width="2" height="25" />
          </g>

          <!-- 装饰性音符符号 -->
          <g fill="#00BCD4" opacity="0.4">
            <text x="30" y="45" font-family="Arial" font-size="20">♪</text>
            <text x="160" y="160" font-family="Arial" font-size="16">♫</text>
          </g>

          <!-- 文字 -->
          <text x="100" y="175" text-anchor="middle" fill="#2196F3" font-family="Arial, sans-serif" font-size="16" font-weight="bold">随心听</text>
        </svg>


      </div>
      <div class="prompt-text">
        <h3>安装随心听</h3>
        <p>将随心听添加到主屏幕，获得更好的使用体验</p>
      </div>
      <div class="prompt-actions">
        <button @click="installPWA" class="install-btn">安装</button>
        <button @click="dismissPrompt" class="dismiss-btn">稍后</button>
      </div>
    </div>
    <button @click="dismissPrompt" class="close-btn">×</button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const showInstallPrompt = ref(false)
let deferredPrompt: any = null

// 检查是否已经安装
const isPWAInstalled = () => {
  return window.matchMedia('(display-mode: standalone)').matches || 
         (window.navigator as any).standalone === true
}

// 检查是否为移动设备
const isMobile = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent)
}

// 检查是否为Safari
const isSafari = () => {
  return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
}

// 处理安装提示事件
const handleBeforeInstallPrompt = (e: Event) => {
  e.preventDefault()
  deferredPrompt = e
  
  // 如果还没有安装且是移动设备，显示安装提示
  if (!isPWAInstalled() && isMobile()) {
    showInstallPrompt.value = true
  }
}

// 安装PWA
const installPWA = async () => {
  if (deferredPrompt) {
    deferredPrompt.prompt()
    const { outcome } = await deferredPrompt.userChoice
    
    if (outcome === 'accepted') {
      console.log('用户接受了PWA安装')
    } else {
      console.log('用户拒绝了PWA安装')
    }
    
    deferredPrompt = null
    showInstallPrompt.value = false
  } else if (isSafari() && isMobile()) {
    // Safari特殊处理
    showSafariInstallInstructions()
  }
}

// 显示Safari安装说明
const showSafariInstallInstructions = () => {
  alert('要安装随心听到主屏幕：\n1. 点击底部的分享按钮 📤\n2. 选择"添加到主屏幕"\n3. 点击"添加"')
}

// 关闭提示
const dismissPrompt = () => {
  showInstallPrompt.value = false
  // 记录用户已经看过提示，避免频繁显示
  localStorage.setItem('pwa-install-dismissed', Date.now().toString())
}

// 检查是否应该显示提示
const shouldShowPrompt = () => {
  const dismissed = localStorage.getItem('pwa-install-dismissed')
  if (dismissed) {
    const dismissedTime = parseInt(dismissed)
    const now = Date.now()
    // 如果距离上次关闭超过7天，再次显示
    return (now - dismissedTime) > 7 * 24 * 60 * 60 * 1000
  }
  return true
}

onMounted(() => {
  // 监听安装提示事件
  window.addEventListener('beforeinstallprompt', handleBeforeInstallPrompt)

  // 对于Safari，检查是否应该显示安装提示
  if (isSafari() && isMobile() && !isPWAInstalled() && shouldShowPrompt()) {
    setTimeout(() => {
      showInstallPrompt.value = true
    }, 3000) // 3秒后显示
  }

  // SVG图标已经内联，不需要额外的错误处理
  console.log('PWA安装提示组件已加载，使用内联SVG图标')
})

onUnmounted(() => {
  window.removeEventListener('beforeinstallprompt', handleBeforeInstallPrompt)
})
</script>

<style scoped>
.pwa-install-prompt {
  position: fixed;
  bottom: 20px;
  left: 20px;
  right: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  z-index: 9999;
  animation: slideUp 0.3s ease-out;
  max-width: 400px;
  margin: 0 auto;
}

.prompt-content {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 12px;
}

.prompt-icon {
  flex-shrink: 0;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f0f8ff, #e8f5e8);
  border-radius: 12px;
  padding: 4px;
}

.app-logo {
  width: 48px;
  height: 48px;
  border-radius: 8px;
}

.prompt-text {
  flex: 1;
  min-width: 0;
}

.prompt-text h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.prompt-text p {
  margin: 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
}

.prompt-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.install-btn {
  background: #4CAF50;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.install-btn:hover {
  background: #45a049;
}

.dismiss-btn {
  background: transparent;
  color: #666;
  border: 1px solid #ddd;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s;
}

.dismiss-btn:hover {
  background: #f5f5f5;
  border-color: #ccc;
}

.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  background: none;
  border: none;
  font-size: 18px;
  color: #999;
  cursor: pointer;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-btn:hover {
  background: #f0f0f0;
  color: #666;
}

@keyframes slideUp {
  from {
    transform: translateY(100px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pwa-install-prompt {
    background: #2d2d2d;
    color: white;
  }
  
  .prompt-text h3 {
    color: white;
  }
  
  .prompt-text p {
    color: #ccc;
  }
  
  .dismiss-btn {
    color: #ccc;
    border-color: #555;
  }
  
  .dismiss-btn:hover {
    background: #3d3d3d;
    border-color: #666;
  }
  
  .close-btn {
    color: #ccc;
  }
  
  .close-btn:hover {
    background: #3d3d3d;
    color: white;
  }
}

/* 移动端适配 */
@media (max-width: 480px) {
  .pwa-install-prompt {
    left: 16px;
    right: 16px;
    bottom: 16px;
  }
  
  .prompt-content {
    padding: 14px 16px;
  }
  
  .prompt-actions {
    flex-direction: column;
    gap: 6px;
  }
  
  .install-btn,
  .dismiss-btn {
    padding: 10px 16px;
    font-size: 14px;
  }
}

/* PWA模式下隐藏 */
@media (display-mode: standalone) {
  .pwa-install-prompt {
    display: none;
  }
}
</style>
