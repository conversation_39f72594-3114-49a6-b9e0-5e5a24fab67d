<template>
  <div class="app-container" :class="{ mobile: isMobile, noElectron: !isElectron, 'pwa-mode': isPWAMode }">
    <n-config-provider :theme="theme === 'dark' ? darkTheme : lightTheme">
      <n-dialog-provider>
        <n-message-provider>
          <router-view></router-view>
          <!-- PWA 安装提示 -->
          <PWAInstallPrompt v-if="!isElectron" />
        </n-message-provider>
      </n-dialog-provider>
    </n-config-provider>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash';
import { darkTheme, lightTheme } from 'naive-ui';
import { computed, nextTick, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';

import homeRouter from '@/router/home';
import { useMenuStore } from '@/store/modules/menu';
import { usePlayerStore } from '@/store/modules/player';
import { useSettingsStore } from '@/store/modules/settings';
import { isElectron, isLyricWindow } from '@/utils';

import { initAudioListeners } from './hooks/MusicHook';
import { isMobile } from './utils';
import { useAppShortcuts } from './utils/appShortcuts';
import { audioService } from './services/audioService';
import { initPWA, isPWAMode as checkPWAMode, setThemeColor } from './utils/pwa';
import PWAInstallPrompt from './components/PWAInstallPrompt.vue';

const { locale } = useI18n();
const settingsStore = useSettingsStore();
const menuStore = useMenuStore();
const playerStore = usePlayerStore();
const router = useRouter();

// 监听语言变化
watch(
  () => settingsStore.setData.language,
  (newLanguage) => {
    if (newLanguage && newLanguage !== locale.value) {
      locale.value = newLanguage;
    }
  },
  { immediate: true }
);

const theme = computed(() => {
  return settingsStore.theme;
});

// PWA 相关
const isPWAMode = computed(() => checkPWAMode());

// 监听主题变化，更新PWA主题颜色
watch(
  () => settingsStore.theme,
  (newTheme) => {
    if (!isElectron) {
      const lightColor = '#4CAF50';
      const darkColor = '#2E7D32';
      setThemeColor(newTheme === 'dark' ? darkColor : lightColor, darkColor);
    }
  },
  { immediate: true }
);

// 监听字体变化并应用
watch(
  () => [settingsStore.setData.fontFamily, settingsStore.setData.fontScope],
  ([newFont, fontScope]) => {
    const appElement = document.body;
    if (newFont && fontScope === 'global') {
      appElement.style.fontFamily = newFont;
    } else {
      appElement.style.fontFamily = '';
    }
  }
);

const handleSetLanguage = (value: string) => {
  console.log('应用语言变更:', value);
  if (value) {
    locale.value = value;
  }
};

if (!isLyricWindow.value) {
  settingsStore.initializeSettings();
  settingsStore.initializeTheme();
  settingsStore.initializeSystemFonts();
  if (isMobile.value) {
    menuStore.setMenus(homeRouter.filter((item) => item.meta.isMobile));
  }
}

handleSetLanguage(settingsStore.setData.language);

// 监听迷你模式状态
if (isElectron) {
  window.api.onLanguageChanged(handleSetLanguage);
  window.electron.ipcRenderer.on('mini-mode', (_, value) => {
    settingsStore.setMiniMode(value);
    if (value) {
      // 存储当前路由
      localStorage.setItem('currentRoute', router.currentRoute.value.path);
      router.push('/mini');
    } else {
      // 恢复当前路由
      const currentRoute = localStorage.getItem('currentRoute');
      if (currentRoute) {
        router.push(currentRoute);
        localStorage.removeItem('currentRoute');
      } else {
        router.push('/');
      }
    }
  });
}

// 使用应用内快捷键
useAppShortcuts();

onMounted(async () => {
  if (isLyricWindow.value) {
    return;
  }

  // 初始化PWA功能（仅在非Electron环境下）
  if (!isElectron) {
    initPWA();
  }

  // 先初始化播放状态
  await playerStore.initializePlayState();
  // 如果有正在播放的音乐，则初始化音频监听器
  if (playerStore.playMusic && playerStore.playMusic.id) {
    // 使用 nextTick 确保 DOM 更新后再初始化
    await nextTick();
    initAudioListeners();
    if (isElectron) {
      window.api.sendSong(cloneDeep(playerStore.playMusic));
    }
  }

  audioService.releaseOperationLock();
});
</script>

<style lang="scss" scoped>
.app-container {
  @apply h-full w-full;
  user-select: none;

  // PWA 模式适配
  &.pwa-mode {
    height: 100vh;
    overflow: hidden;

    // 适配安全区域
    padding-left: max(0px, env(safe-area-inset-left));
    padding-right: max(0px, env(safe-area-inset-right));
    padding-top: max(0px, env(safe-area-inset-top));
    padding-bottom: max(0px, env(safe-area-inset-bottom));
  }
}

.mobile {
  .text-base {
    font-size: 14px !important;
  }

  // 移动端PWA模式特殊处理
  &.pwa-mode {
    // 确保内容不被状态栏遮挡
    .main-content {
      padding-top: max(20px, env(safe-area-inset-top));
    }
  }
}

.html:has(.mobile) {
  font-size: 14px;
}

// 全局PWA样式
:global(.pwa-mode) {
  // 防止iOS Safari的橡皮筋效果
  body {
    position: fixed;
    width: 100%;
    height: 100%;
    overflow: hidden;
    overscroll-behavior: none;
  }

  // 优化滚动
  .scrollable {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  // 优化触摸反馈
  button, .clickable {
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0.1);
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    user-select: none;
  }
}
</style>
